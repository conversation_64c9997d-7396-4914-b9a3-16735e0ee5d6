-- =====================================================
-- SISTEM TEMPAHAN BILIK MESYUARAT
-- Pangkalan Data dalam Bahasa Melayu
-- Dicipta: 2025-08-04
-- =====================================================

-- Mencipta pangkalan data
CREATE DATABASE IF NOT EXISTS sistem_tempahan_bilik CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE sistem_tempahan_bilik;

-- =====================================================
-- JADUAL GRED
-- =====================================================
DROP TABLE IF EXISTS `tgred`;

CREATE TABLE `tgred` (
    `id` int(11) NOT NULL,
    `gred` varchar(10) DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- =====================================================
-- JADUAL JAWATAN
-- =====================================================
DROP TABLE IF EXISTS `tjawatan`;

CREATE TABLE `tjawatan` (
    `id` int(100) NOT NULL,
    `jawatan` varchar(100) DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- =====================================================
-- JADUAL BAHAGIAN
-- =====================================================
DROP TABLE IF EXISTS `tbahagian`;

CREATE TABLE `tbahagian` (
    `id` int(6) NOT NULL,
    `bahagian` varchar(100) DEFAULT NULL,
    `idptj` int(6) DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- =====================================================
-- JADUAL UNIT
-- =====================================================
DROP TABLE IF EXISTS `tunit`;

CREATE TABLE `tunit` (
    `id` int(6) NOT NULL,
    `unit` varchar(100) DEFAULT NULL,
    `idbahagian` int(6) DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `fk_unit_bahagian` (`idbahagian`),
    CONSTRAINT `fk_unit_bahagian` FOREIGN KEY (`idbahagian`) REFERENCES `tbahagian` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- =====================================================
-- JADUAL PENGGUNA
-- =====================================================
CREATE TABLE pengguna (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nokp VARCHAR(12) UNIQUE NOT NULL,
    kata_laluan VARCHAR(255) NOT NULL,
    nama_penuh VARCHAR(100) NOT NULL,
    emel VARCHAR(100) UNIQUE NOT NULL,
    no_telefon VARCHAR(20),
    bahagian_id INT,
    unit_id INT,
    jawatan_id INT,
    gred_id INT,
    peranan ENUM('pentadbir', 'pengguna', 'penyelaras') DEFAULT 'pengguna',
    status ENUM('aktif', 'tidak_aktif') DEFAULT 'aktif',
    tarikh_daftar TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    tarikh_kemaskini TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (bahagian_id) REFERENCES tbahagian(id) ON DELETE SET NULL,
    FOREIGN KEY (unit_id) REFERENCES tunit(id) ON DELETE SET NULL,
    FOREIGN KEY (jawatan_id) REFERENCES tjawatan(id) ON DELETE SET NULL,
    FOREIGN KEY (gred_id) REFERENCES tgred(id) ON DELETE SET NULL
);



-- =====================================================
-- JADUAL BILIK MESYUARAT
-- =====================================================
DROP TABLE IF EXISTS `tbilik_mesyuarat`;

CREATE TABLE tbilik_mesyuarat (
    id INT PRIMARY KEY,
    nama_bilik_mesyuarat VARCHAR(100) NOT NULL,
    kapasiti INT NOT NULL,
    bahagian INT,
    lokasi VARCHAR(200),
    tingkat VARCHAR(20),
    penerangan TEXT,
    kemudahan JSON,
    kadar_sejam DECIMAL(10,2) DEFAULT 0.00,
    gambar_url VARCHAR(255),
    status ENUM('tersedia', 'tidak_tersedia', 'penyelenggaraan') DEFAULT 'tersedia',
    tarikh_cipta TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    tarikh_kemaskini TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (bahagian) REFERENCES tbahagian(id) ON DELETE SET NULL
);

-- =====================================================
-- JADUAL PENYELARAS BILIK
-- =====================================================
DROP TABLE IF EXISTS `tbilik_penyelaras`;

CREATE TABLE tbilik_penyelaras (
    id INT PRIMARY KEY,
    idbilik_mesyuarat INT,
    idpenyelaras INT,
    FOREIGN KEY (idbilik_mesyuarat) REFERENCES tbilik_mesyuarat(id) ON DELETE CASCADE,
    FOREIGN KEY (idpenyelaras) REFERENCES pengguna(id) ON DELETE CASCADE
);

-- =====================================================
-- JADUAL TEMPAHAN
-- =====================================================
CREATE TABLE tempahan (
    id INT AUTO_INCREMENT PRIMARY KEY,
    kod_tempahan VARCHAR(20) UNIQUE NOT NULL,
    pengguna_id INT NOT NULL,
    bilik_id INT NOT NULL,
    tarikh_tempahan DATE NOT NULL,
    masa_mula TIME NOT NULL,
    masa_tamat TIME NOT NULL,
    tujuan VARCHAR(255) NOT NULL,
    agenda TEXT,
    bilangan_peserta INT DEFAULT 1,
    keperluan_khas TEXT,
    status ENUM('menunggu', 'diluluskan', 'ditolak', 'dibatalkan', 'selesai') DEFAULT 'menunggu',
    jumlah_kos DECIMAL(10,2) DEFAULT 0.00,
    catatan_pentadbir TEXT,
    diluluskan_oleh INT,
    tarikh_kelulusan TIMESTAMP NULL,
    tarikh_tempahan_dibuat TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    tarikh_kemaskini TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (pengguna_id) REFERENCES pengguna(id) ON DELETE CASCADE,
    FOREIGN KEY (bilik_id) REFERENCES tbilik_mesyuarat(id) ON DELETE CASCADE,
    FOREIGN KEY (diluluskan_oleh) REFERENCES pengguna(id) ON DELETE SET NULL,
    UNIQUE KEY tempahan_unik (bilik_id, tarikh_tempahan, masa_mula, masa_tamat)
);

-- =====================================================
-- JADUAL SEJARAH TEMPAHAN
-- =====================================================
CREATE TABLE sejarah_tempahan (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tempahan_id INT NOT NULL,
    tindakan ENUM('dicipta', 'dikemaskini', 'diluluskan', 'ditolak', 'dibatalkan') NOT NULL,
    dilakukan_oleh INT NOT NULL,
    nilai_lama JSON,
    nilai_baru JSON,
    catatan TEXT,
    tarikh_tindakan TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (tempahan_id) REFERENCES tempahan(id) ON DELETE CASCADE,
    FOREIGN KEY (dilakukan_oleh) REFERENCES pengguna(id) ON DELETE CASCADE
);

-- =====================================================
-- JADUAL TETAPAN SISTEM
-- =====================================================
CREATE TABLE tetapan_sistem (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nama_tetapan VARCHAR(100) UNIQUE NOT NULL,
    nilai TEXT NOT NULL,
    penerangan TEXT,
    jenis ENUM('teks', 'nombor', 'boolean', 'json') DEFAULT 'teks',
    tarikh_kemaskini TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- =====================================================
-- JADUAL NOTIFIKASI
-- =====================================================
CREATE TABLE notifikasi (
    id INT AUTO_INCREMENT PRIMARY KEY,
    pengguna_id INT NOT NULL,
    tajuk VARCHAR(200) NOT NULL,
    mesej TEXT NOT NULL,
    jenis ENUM('maklumat', 'amaran', 'kejayaan', 'ralat') DEFAULT 'maklumat',
    dibaca BOOLEAN DEFAULT FALSE,
    tempahan_id INT NULL,
    tarikh_cipta TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (pengguna_id) REFERENCES pengguna(id) ON DELETE CASCADE,
    FOREIGN KEY (tempahan_id) REFERENCES tempahan(id) ON DELETE CASCADE
);

-- =====================================================
-- INDEKS UNTUK PRESTASI YANG LEBIH BAIK
-- =====================================================
CREATE INDEX idx_tempahan_tarikh ON tempahan(tarikh_tempahan);
CREATE INDEX idx_tempahan_bilik_tarikh ON tempahan(bilik_id, tarikh_tempahan);
CREATE INDEX idx_tempahan_pengguna ON tempahan(pengguna_id);
CREATE INDEX idx_tempahan_status ON tempahan(status);
CREATE INDEX idx_sejarah_tempahan ON sejarah_tempahan(tempahan_id);
CREATE INDEX idx_notifikasi_pengguna ON notifikasi(pengguna_id, dibaca);

-- =====================================================
-- DATA AWAL SISTEM
-- =====================================================

-- Memasukkan data gred
INSERT INTO `tgred`(`id`,`gred`) VALUES
(1,'B22'),
(2,'C27'),
(3,'C32'),
(4,'C41'),
(5,'C44'),
(6,'C48'),
(7,'C52'),
(8,'F29'),
(9,'F32'),
(10,'F41'),
(11,'F44'),
(12,'FT17'),
(13,'J17'),
(14,'J29'),
(15,'J41'),
(16,'J44'),
(17,'J48'),
(18,'KP17'),
(19,'M41'),
(20,'M44'),
(21,'M48'),
(22,'M52'),
(23,'N1'),
(24,'N17'),
(25,'N22'),
(26,'N26'),
(27,'N27'),
(28,'N28'),
(29,'N32'),
(30,'N36'),
(31,'N4'),
(32,'N41'),
(33,'R1'),
(34,'R3'),
(35,'R4'),
(36,'R6'),
(37,'S41'),
(38,'S44'),
(39,'S48'),
(40,'U17'),
(41,'U29'),
(42,'U32'),
(43,'U36'),
(44,'U38'),
(45,'U41'),
(46,'U42'),
(47,'U44'),
(48,'U48'),
(49,'U52'),
(50,'U54'),
(51,'UD44'),
(52,'UD48'),
(53,'UD51'),
(54,'UD52'),
(55,'UD54'),
(56,'W17'),
(57,'W22'),
(58,'W27'),
(59,'W36'),
(60,'W44'),
(62,'M48'),
(63,'M54'),
(64,'H11'),
(65,'N11'),
(66,'R11');

-- Memasukkan data jawatan
INSERT INTO `tjawatan`(`id`,`jawatan`) VALUES
(2,'JURUAUDIO VISUAL'),
(3,'JURURAWAT'),
(4,'JURURAWAT PERGIGIAN'),
(5,'JURUTEKNIK'),
(6,'JURUTEKNIK KOMPUTER'),
(7,'JURUTEKNOLOGI MAKMAL PERUBATAN'),
(8,'JURUTERA (AWAM)'),
(9,'JURUTERA (ELEKTRIK)'),
(10,'JURUTERA (KESIHATAN UMUM)'),
(11,'JURUTERA (MEKANIKAL)'),
(12,'PEGAWAI FARMASI'),
(14,'PEGAWAI KAUNSELOR'),
(15,'PEGAWAI KESIHATAN PERSEKITARAN'),
(16,'PEGAWAI KHIDMAT PELANGGAN'),
(18,'PEGAWAI PERGIGIAN'),
(19,'PEGAWAI PERGIGIAN'),
(20,'PEGAWAI PERUBATAN'),
(22,'PEGAWAI SAINS'),
(23,'PEGAWAI SAINS (KIMIA HAYAT)'),
(24,'PEGAWAI SAINS (PEGAWAI ZAT MAKANAN)'),
(26,'PEGAWAI TADBIR DAN DIPLOMATIK'),
(27,'PEGAWAI TEKNOLOGI MAKANAN'),
(28,'PEGAWAI TEKNOLOGI MAKLUMAT'),
(29,'PEKERJA AWAM'),
(30,'PEKERJA RENDAH AWAM'),
(31,'PEMANDU KENDERAAN'),
(32,'PEMBANTU AM PEJABAT'),
(33,'PEMBANTU KESELAMATAN'),
(34,'PEMBANTU KESIHATAN AWAM'),
(35,'PEMBANTU TADBIR (KESETIAUSAHAAN)'),
(36,'PEMBANTU TADBIR (KEWANGAN)'),
(37,'PEMBANTU TADBIR (P/O)'),
(39,'PEMBANTU TEKNIK'),
(40,'PEN. PEG. TEKNOLOGI MAKANAN'),
(41,'PENOLONG AKAUNTAN'),
(42,'PENOLONG JURUTERA'),
(43,'PENOLONG PEGAWAI KESIHATAN PERSEKITARAN'),
(44,'PENOLONG PEGAWAI PERUBATAN'),
(45,'PENOLONG PEGAWAI SAINS'),
(46,'PENOLONG PEGAWAI TADBIR'),
(47,'PEN. PEGAWAI TADBIR (REKOD PERUBATAN)'),
(48,'PEN. PEGAWAI TEKNOLOGI MAKLUMAT'),
(49,'PEREKA'),
(50,'SETIAUSAHA PEJABAT'),
(52,'TIMB. PENGARAH KESIHATAN NEGERI (PENGURUSAN)'),
(53,'PENGARAH KESIHATAN NEGERI'),
(54,'PENGARAH HOSPITAL');

-- Memasukkan data bahagian
INSERT INTO `tbahagian`(`id`,`bahagian`,`idptj`) VALUES
(1,'Kesihatan Awam',NULL),
(2,'Perubatan',NULL),
(3,'Pengurusan',NULL),
(4,'Pergigian',NULL),
(5,'Farmasi',NULL),
(6,'Keselamatan & Kualiti Makanan',NULL);

-- Memasukkan data unit
INSERT INTO `tunit`(`id`,`unit`,`idbahagian`) VALUES
(2,'ALAM SEKITAR',1),
(3,'INSPEKTORAT & PERUNDANGAN',1),
(4,'KEJURUTERAAN',1),
(5,'KESIHATAN AWAM',1),
(6,'KESIHATAN KELUARGA',1),
(7,'KESIHATAN PRIMER',1),
(8,'KPAS',1),
(9,'PEMAKANAN',1),
(10,'PEMBANGUNAN KELUARGA',1),
(11,'PENYAKIT BERJANGKIT',1),
(12,'PENYAKIT TIDAK BERJANGKIT',1),
(13,'PROMOSI KESIHATAN',1),
(14,'TPKN(KA)',1),
(15,'Pengurusan Perubatan',2),
(17,'KAUNSELING',3),
(18,'KEJURURAWATAN',3),
(19,'KEWANGAN',3),
(20,'LATIHAN',3),
(21,'PEMBANGUNAN',3),
(22,'PENGARAH',3),
(23,'KHIDMAT PENGURUSAN',3),
(24,'PERJAWATAN',3),
(25,'SM',3),
(26,'TPKN(U)',3),
(31,'Amalan & Perkembangan Farmasi',5),
(32,'Pengurusan Farmasi',5),
(33,'Cawangan Penguatkuasaan Farmasi',5),
(34,'BKKM',6),
(54,'ICT',3),
(55,'UKAPS',2),
(56,'Kejuruteraan',2),
(57,'Rekod Perubatan',2),
(58,'Pengurusan Pergigian',4),
(59,'Kualiti',2),
(60,'Unit PPP',2),
(61,'HIV/STI',1),
(62,'PEROLEHAN DAN ASET',3);

-- Memasukkan pengguna pentadbir lalai
INSERT INTO pengguna (nokp, kata_laluan, nama_penuh, emel, peranan, bahagian_id, unit_id, jawatan_id, gred_id) VALUES
('123456789012', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Pentadbir Sistem', '<EMAIL>', 'pentadbir', 3, 22, 26, 32),
('234567890123', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Dr. Ahmad bin Ali', '<EMAIL>', 'penyelaras', 2, 15, 20, 45),
('345678901234', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Siti binti Hassan', '<EMAIL>', 'pengguna', 1, 5, 3, 47);

-- Memasukkan data bilik mesyuarat
INSERT INTO `tbilik_mesyuarat`(`id`,`nama_bilik_mesyuarat`,`kapasiti`,`bahagian`) VALUES
(1,'Bilik Mesyuarat Utama',60,3),
(2,'Bilik Command Centre',25,1),
(3,'Bilik Mesyuarat Kesihatan Awam',35,1),
(4,'Bilik Kaunseling Kelompok',8,3),
(5,'Bilik Mesyuarat Perubatan',20,2),
(6,'Bilik Mesyuarat Pengurusan',35,3),
(7,'Bilik Perbincangan Pengarah',10,3),
(8,'Bilik Mesyuarat Pergigian',25,4),
(9,'Bilik Sumber Kualiti Perubatan',10,2),
(10,'Bilik Mesyuarat Farmasi',35,5),
(12,'TESTING BILIK (HANYA UNTUK PENGUJIAN ICT SAHAJA)',10,3);

-- Memasukkan data penyelaras bilik
INSERT INTO `tbilik_penyelaras`(`id`,`idbilik_mesyuarat`,`idpenyelaras`) VALUES
(1,1,1),
(6,4,1),
(8,6,1),
(10,7,1),
(12,5,2),
(17,8,2),
(23,6,1),
(24,1,1),
(25,7,1),
(27,10,2),
(30,9,2),
(34,2,1),
(36,3,3),
(38,5,2),
(39,5,2),
(40,5,2),
(42,12,1),
(43,12,1),
(44,12,1),
(47,12,1),
(50,10,2);

-- Memasukkan data bilik mesyuarat
INSERT INTO bilik_mesyuarat (nama_bilik, kod_bilik, kapasiti, lokasi, tingkat, penerangan, kemudahan, bahagian_id, penyelaras_id) VALUES
('Bilik Mesyuarat Utama', 'BMU-001', 50, 'Blok A', 'Tingkat 3', 'Bilik mesyuarat utama untuk mesyuarat besar', '["Projektor", "Sistem Audio", "Papan Putih", "WiFi", "Penyaman Udara"]', 1, 2),
('Bilik Mesyuarat Eksekutif', 'BME-001', 20, 'Blok A', 'Tingkat 5', 'Bilik mesyuarat untuk pengurusan atasan', '["Projektor", "Video Conference", "Papan Putih", "WiFi", "Penyaman Udara"]', 3, 2),
('Bilik Perbincangan Perubatan', 'MED-001', 15, 'Blok B', 'Tingkat 2', 'Bilik untuk perbincangan perubatan', '["Komputer", "Projektor", "Papan Putih", "WiFi", "Penyaman Udara"]', 2, 2),
('Bilik Latihan', 'LAT-001', 30, 'Blok C', 'Tingkat 1', 'Bilik untuk sesi latihan dan pembangunan', '["Projektor", "Sistem Audio", "Papan Putih", "WiFi", "Penyaman Udara", "Meja Boleh Alih"]', 1, 2);

-- Memasukkan tetapan sistem
INSERT INTO tetapan_sistem (nama_tetapan, nilai, penerangan, jenis) VALUES
('nama_organisasi', 'Sistem Tempahan Bilik Mesyuarat', 'Nama organisasi yang dipaparkan dalam sistem', 'teks'),
('masa_tempahan_minimum', '30', 'Masa minimum tempahan dalam minit', 'nombor'),
('masa_tempahan_maksimum', '480', 'Masa maksimum tempahan dalam minit (8 jam)', 'nombor'),
('hari_tempahan_awal', '30', 'Berapa hari awal boleh membuat tempahan', 'nombor'),
('emel_notifikasi', 'true', 'Hantar notifikasi melalui emel', 'boolean'),
('kelulusan_automatik', 'false', 'Tempahan diluluskan secara automatik', 'boolean');
