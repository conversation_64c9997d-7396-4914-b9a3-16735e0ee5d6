# Sistem Kawalan Admin

## <PERSON><PERSON><PERSON><PERSON> Ke<PERSON>han

Sistem Kawalan Admin adalah modul khusus untuk pengguna dengan `profile_id = 2` (Admin) yang membolehkan pengurusan data asas sistem tempahan bilik mesyuarat.

## Ciri-ciri Utama

### 1. Kawalan Unit
- **Lokasi**: `admin/kawalan_unit.php`
- **Fungsi**: Menguruskan unit dalam organisasi
- **Operasi CRUD**:
  - Tambah unit baru dengan ID unik
  - Edit nama unit dan bahagian yang berkaitan
  - Hapus unit (dengan semakan kebergantungan)
  - Senarai semua unit dengan maklumat bahagian

### 2. Kawalan Bahagian
- **Lokasi**: `admin/kawalan_bahagian.php`
- **Fungsi**: Menguruskan bahagian dalam organisasi
- **Operasi CRUD**:
  - Tambah bahagian baru dengan ID unik
  - Edit nama bahagian dan ID PTJ
  - Hapus bahagian (dengan semakan kebergantungan)
  - <PERSON><PERSON>i semua bahagian dengan statistik penggunaan

### 3. <PERSON><PERSON><PERSON> Bilik Mesyuarat
- **Lokasi**: `admin/kawalan_kemudahan.php`
- **Fungsi**: Menguruskan kemudahan yang tersedia untuk bilik mesyuarat
- **Operasi**:
  - Tambah kemudahan baru
  - Edit nama kemudahan (akan kemaskini semua bilik yang menggunakan)
  - Hapus kemudahan (akan buang dari semua bilik)
  - Senarai kemudahan dengan statistik penggunaan

## Akses Sistem

### Syarat Akses
- Pengguna mesti log masuk
- `profile_id` mesti bersamaan dengan 2 (Admin)
- Jika tidak memenuhi syarat, akan diarahkan ke dashboard

### Menu Akses
- Menu "Kawalan" akan muncul dalam dropdown profil pengguna (header)
- Hanya visible untuk admin (`profile_id = 2`)

## Struktur Fail

```
admin/
├── kawalan.php              # Halaman utama kawalan
├── kawalan_unit.php         # Pengurusan unit
├── kawalan_bahagian.php     # Pengurusan bahagian
├── kawalan_kemudahan.php    # Pengurusan kemudahan
└── README_KAWALAN.md        # Dokumentasi ini
```

## Jadual Database Yang Terlibat

### 1. tbahagian
- `id` - ID unik bahagian
- `bahagian` - Nama bahagian
- `idptj` - ID Pusat Tanggungjawab (opsional)

### 2. tunit
- `id` - ID unik unit
- `unit` - Nama unit
- `idbahagian` - Foreign key ke tbahagian

### 3. tbilik_mesyuarat
- `kemudahan` - JSON array kemudahan yang tersedia

## Ciri Keselamatan

### Semakan Kebergantungan
- **Unit**: Tidak boleh dihapus jika digunakan oleh pengguna
- **Bahagian**: Tidak boleh dihapus jika digunakan oleh unit, pengguna, atau bilik
- **Kemudahan**: Akan dihapus dari semua bilik yang menggunakan

### Validasi Input
- Semua input dibersihkan menggunakan `htmlspecialchars()`
- Semakan ID unik untuk unit dan bahagian
- Validasi medan wajib

## Statistik dan Laporan

### Halaman Utama Kawalan
- Jumlah unit dalam sistem
- Jumlah bahagian dalam sistem
- Jumlah bilik mesyuarat
- Jumlah pengguna aktif

### Statistik Terperinci
- **Unit**: Menunjukkan bahagian yang berkaitan
- **Bahagian**: Menunjukkan jumlah unit, pengguna, dan bilik
- **Kemudahan**: Menunjukkan jumlah bilik yang menggunakan

## Teknologi Yang Digunakan

- **Backend**: PHP dengan PDO
- **Frontend**: Bootstrap 5 dengan Bootstrap Icons
- **Database**: MySQL/MariaDB
- **JavaScript**: Vanilla JS untuk modal dan interaksi

## Cara Penggunaan

1. Log masuk sebagai admin (`profile_id = 2`)
2. Klik pada dropdown profil di header
3. Pilih "Kawalan"
4. Pilih modul yang ingin diuruskan:
   - Kawalan Unit
   - Kawalan Bahagian
   - Kawalan Kemudahan

## Nota Penting

- Sistem menggunakan JSON untuk menyimpan kemudahan bilik mesyuarat
- Perubahan pada kemudahan akan mempengaruhi semua bilik yang menggunakan
- Backup database sebelum melakukan operasi hapus yang besar
- Sistem mempunyai breadcrumb navigation untuk kemudahan navigasi

## Sokongan dan Penyelenggaraan

Untuk sebarang isu atau pertanyaan mengenai sistem kawalan ini, sila rujuk kepada dokumentasi sistem utama atau hubungi pentadbir sistem.
