<?php
/**
 * Halaman Profil Pengguna
 * Sistem Tempahan Bilik Mesyuarat
 */

$tajuk_halaman = 'Profil Saya';
require_once '../config/sistem_config.php';

// Semak login
perluLogin();

// Inisialisasi pangkalan data
try {
    $db = new Database();
} catch (Exception $e) {
    die('Ralat sambungan pangkalan data: ' . $e->getMessage());
}

$pengguna = $pengguna ?? null;

$mesej_ralat = '';
$mesej_kejayaan = '';
$mesej_amaran = '';

// Fungsi untuk memeriksa jika kolum wujud
function checkColumnExists($db, $table, $column) {
    try {
        $result = $db->fetchAll("SHOW COLUMNS FROM `$table` LIKE '$column'");
        return count($result) > 0;
    } catch (Exception $e) {
        return false;
    }
}

// Periksa jika kolum yang diperlukan wujud
$columns_exist = [
    'bahagian_id' => checkColumnExists($db, 'pengguna', 'bahagian_id'),
    'unit_id' => checkColumnExists($db, 'pengguna', 'unit_id'),
    'jawatan_id' => checkColumnExists($db, 'pengguna', 'jawatan_id'),
    'gred_id' => checkColumnExists($db, 'pengguna', 'gred_id')
];

// Dapatkan maklumat pengguna lengkap
try {
    if ($columns_exist['bahagian_id'] && $columns_exist['unit_id'] &&
        $columns_exist['jawatan_id'] && $columns_exist['gred_id']) {

        // Semua kolum wujud, gunakan query dengan JOIN
        $sql = "SELECT p.*, b.bahagian, u.unit, j.jawatan, g.gred
                FROM pengguna p
                LEFT JOIN tbahagian b ON p.bahagian_id = b.id
                LEFT JOIN tunit u ON p.unit_id = u.id
                LEFT JOIN tjawatan j ON p.jawatan_id = j.id
                LEFT JOIN tgred g ON p.gred_id = g.id
                WHERE p.id = ?";
        $pengguna = $db->fetch($sql, [$_SESSION['pengguna_id']]);

        if (!$pengguna) {
            $mesej_ralat = 'Maklumat pengguna tidak dijumpai.';
        } else {
            // Pastikan semua field ID ada dengan nilai default null jika tidak wujud
            $pengguna['bahagian_id'] = $pengguna['bahagian_id'] ?? null;
            $pengguna['unit_id'] = $pengguna['unit_id'] ?? null;
            $pengguna['jawatan_id'] = $pengguna['jawatan_id'] ?? null;
            $pengguna['gred_id'] = $pengguna['gred_id'] ?? null;
        }
    } else {
        // Beberapa kolum tidak wujud, gunakan query asas sahaja
        $sql_simple = "SELECT * FROM pengguna WHERE id = ?";
        $pengguna = $db->fetch($sql_simple, [$_SESSION['pengguna_id']]);

        if (!$pengguna) {
            $mesej_ralat = 'Maklumat pengguna tidak dijumpai.';
        } else {
            // Set nilai default untuk field yang tidak wujud
            $pengguna['bahagian'] = 'Tidak ditetapkan';
            $pengguna['unit'] = 'Tidak ditetapkan';
            $pengguna['jawatan'] = 'Tidak ditetapkan';
            $pengguna['gred'] = 'Tidak ditetapkan';

            // Set ID fields berdasarkan kolum yang wujud
            $pengguna['bahagian_id'] = $columns_exist['bahagian_id'] ? ($pengguna['bahagian_id'] ?? null) : null;
            $pengguna['unit_id'] = $columns_exist['unit_id'] ? ($pengguna['unit_id'] ?? null) : null;
            $pengguna['jawatan_id'] = $columns_exist['jawatan_id'] ? ($pengguna['jawatan_id'] ?? null) : null;
            $pengguna['gred_id'] = $columns_exist['gred_id'] ? ($pengguna['gred_id'] ?? null) : null;
        }
    }
} catch (Exception $e) {
    $mesej_ralat = 'Ralat memuatkan maklumat pengguna: ' . $e->getMessage();
}

// Periksa jika ada kolum yang tidak wujud dan beri amaran
$missing_columns = [];
foreach ($columns_exist as $column => $exists) {
    if (!$exists) {
        $missing_columns[] = $column;
    }
}

if (!empty($missing_columns)) {
    $mesej_amaran = 'Struktur database tidak lengkap. Kolum yang hilang: ' . implode(', ', $missing_columns) .
                   '. <a href="../admin/periksa_database.php" class="alert-link">Klik di sini untuk memperbaiki database</a>.';
}

// Proses kemaskini profil
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['kemaskini_profil'])) {
    $nama_penuh = bersihkanInput($_POST['nama_penuh']);
    $emel = bersihkanInput($_POST['emel']);
    $no_telefon = bersihkanInput($_POST['no_telefon']);
    $bahagian_id = !empty($_POST['bahagian_id']) ? (int)$_POST['bahagian_id'] : null;
    $unit_id = !empty($_POST['unit_id']) ? (int)$_POST['unit_id'] : null;
    $jawatan_id = !empty($_POST['jawatan_id']) ? (int)$_POST['jawatan_id'] : null;
    $gred_id = !empty($_POST['gred_id']) ? (int)$_POST['gred_id'] : null;

    // Validasi input
    if (empty($nama_penuh) || empty($emel)) {
        $mesej_ralat = 'Nama penuh dan emel adalah wajib.';
    } elseif (!filter_var($emel, FILTER_VALIDATE_EMAIL)) {
        $mesej_ralat = 'Format emel tidak sah.';
    } else {
        try {
            // Semak jika emel sudah digunakan oleh pengguna lain
            $sql_check = "SELECT id FROM pengguna WHERE emel = ? AND id != ?";
            $existing = $db->fetch($sql_check, [$emel, $_SESSION['pengguna_id']]);

            if ($existing) {
                $mesej_ralat = 'Emel sudah digunakan oleh pengguna lain.';
            } else {
                // Kemaskini maklumat pengguna berdasarkan kolum yang wujud
                $update_fields = ["nama_penuh = ?", "emel = ?", "no_telefon = ?"];
                $update_params = [$nama_penuh, $emel, $no_telefon];

                // Tambah field yang wujud sahaja
                if ($columns_exist['bahagian_id']) {
                    $update_fields[] = "bahagian_id = ?";
                    $update_params[] = $bahagian_id;
                }
                if ($columns_exist['unit_id']) {
                    $update_fields[] = "unit_id = ?";
                    $update_params[] = $unit_id;
                }
                if ($columns_exist['jawatan_id']) {
                    $update_fields[] = "jawatan_id = ?";
                    $update_params[] = $jawatan_id;
                }
                if ($columns_exist['gred_id']) {
                    $update_fields[] = "gred_id = ?";
                    $update_params[] = $gred_id;
                }

                $update_params[] = $_SESSION['pengguna_id']; // WHERE clause parameter

                $sql_update = "UPDATE pengguna SET " . implode(", ", $update_fields) . " WHERE id = ?";
                $result = $db->query($sql_update, $update_params);

                if ($result) {
                    // Kemaskini session
                    $_SESSION['nama_penuh'] = $nama_penuh;
                    $_SESSION['emel'] = $emel;

                    // Dapatkan nama bahagian, unit, jawatan, gred yang baru jika kolum wujud
                    if ($columns_exist['bahagian_id'] && $columns_exist['unit_id'] &&
                        $columns_exist['jawatan_id'] && $columns_exist['gred_id']) {

                        $sql_new = "SELECT b.bahagian, u.unit, j.jawatan, g.gred
                                    FROM pengguna p
                                    LEFT JOIN tbahagian b ON p.bahagian_id = b.id
                                    LEFT JOIN tunit u ON p.unit_id = u.id
                                    LEFT JOIN tjawatan j ON p.jawatan_id = j.id
                                    LEFT JOIN tgred g ON p.gred_id = g.id
                                    WHERE p.id = ?";
                        $new_data = $db->fetch($sql_new, [$_SESSION['pengguna_id']]);

                        $_SESSION['bahagian'] = $new_data['bahagian'] ?? 'Tidak ditetapkan';
                        $_SESSION['unit'] = $new_data['unit'] ?? 'Tidak ditetapkan';
                        $_SESSION['jawatan'] = $new_data['jawatan'] ?? 'Tidak ditetapkan';
                        $_SESSION['gred'] = $new_data['gred'] ?? 'Tidak ditetapkan';
                    }

                    // Log aktiviti
                    logAktiviti($_SESSION['pengguna_id'], 'Kemaskini Profil', 'Pengguna mengemaskini maklumat profil');

                    $mesej_kejayaan = 'Profil berjaya dikemaskini.';

                    // Refresh data pengguna
                    $pengguna = $db->fetch($sql, [$_SESSION['pengguna_id']]);
                } else {
                    $mesej_ralat = 'Ralat semasa mengemaskini profil.';
                }
            }
        } catch (Exception $e) {
            $mesej_ralat = 'Ralat sistem: ' . $e->getMessage();
        }
    }
}

// Dapatkan senarai untuk dropdown
try {
    $sql_bahagian = "SELECT id, bahagian FROM tbahagian ORDER BY bahagian";
    $senarai_bahagian = $db->fetchAll($sql_bahagian);
} catch (Exception $e) {
    $senarai_bahagian = [];
}

try {
    $sql_jawatan = "SELECT id, jawatan FROM tjawatan ORDER BY jawatan";
    $senarai_jawatan = $db->fetchAll($sql_jawatan);
} catch (Exception $e) {
    $senarai_jawatan = [];
}

try {
    $sql_gred = "SELECT id, gred FROM tgred ORDER BY gred";
    $senarai_gred = $db->fetchAll($sql_gred);
} catch (Exception $e) {
    $senarai_gred = [];
}

// Dapatkan senarai unit berdasarkan bahagian
try {
    $sql_unit = "SELECT id, unit, idbahagian FROM tunit ORDER BY unit";
    $senarai_unit = $db->fetchAll($sql_unit);
} catch (Exception $e) {
    $senarai_unit = [];
}

require_once '../includes/header_sistem.php';
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0">Profil Saya</h1>
                <p class="text-muted mb-0">Lihat dan kemaskini maklumat profil anda</p>
            </div>
        </div>
    </div>
</div>

<?php if (!empty($mesej_ralat)): ?>
    <div class="alert alert-danger">
        <i class="bi bi-exclamation-triangle me-2"></i><?= $mesej_ralat ?>
    </div>
<?php endif; ?>

<?php if (!empty($mesej_kejayaan)): ?>
    <div class="alert alert-success">
        <i class="bi bi-check-circle me-2"></i><?= $mesej_kejayaan ?>
    </div>
<?php endif; ?>

<?php if (!empty($mesej_amaran)): ?>
    <div class="alert alert-warning">
        <i class="bi bi-exclamation-triangle me-2"></i><?= $mesej_amaran ?>
    </div>
<?php endif; ?>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-person-gear me-2"></i>Kemaskini Profil</h5>
            </div>
            <div class="card-body">
                <?php if ($pengguna): ?>
                <form method="POST" action="">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="nokp" class="form-label">No. Kad Pengenalan</label>
                            <input type="text" class="form-control" id="nokp"
                                   value="<?= htmlspecialchars($pengguna['nokp']) ?>" readonly>
                            <small class="text-muted">No. KP tidak boleh diubah</small>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="nama_penuh" class="form-label">Nama Penuh *</label>
                            <input type="text" class="form-control" id="nama_penuh" name="nama_penuh"
                                   value="<?= htmlspecialchars($pengguna['nama_penuh']) ?>" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="emel" class="form-label">Emel *</label>
                            <input type="email" class="form-control" id="emel" name="emel"
                                   value="<?= htmlspecialchars($pengguna['emel']) ?>" required>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="no_telefon" class="form-label">No. Telefon</label>
                            <input type="tel" class="form-control" id="no_telefon" name="no_telefon"
                                   value="<?= htmlspecialchars($pengguna['no_telefon'] ?? '') ?>">
                        </div>
                    </div>

                    <?php if ($columns_exist['bahagian_id'] || $columns_exist['unit_id']): ?>
                    <div class="row">
                        <?php if ($columns_exist['bahagian_id']): ?>
                        <div class="col-md-6 mb-3">
                            <label for="bahagian_id" class="form-label">Bahagian</label>
                            <select class="form-select" id="bahagian_id" name="bahagian_id" onchange="updateUnitOptions()">
                                <option value="">Pilih Bahagian</option>
                                <?php if (!empty($senarai_bahagian)): ?>
                                    <?php foreach ($senarai_bahagian as $bahagian): ?>
                                        <option value="<?= $bahagian['id'] ?>"
                                                <?= (isset($pengguna['bahagian_id']) && $pengguna['bahagian_id'] == $bahagian['id']) ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($bahagian['bahagian']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <option value="" disabled>Tiada data bahagian</option>
                                <?php endif; ?>
                            </select>
                        </div>
                        <?php endif; ?>

                        <?php if ($columns_exist['unit_id']): ?>
                        <div class="col-md-6 mb-3">
                            <label for="unit_id" class="form-label">Unit</label>
                            <select class="form-select" id="unit_id" name="unit_id">
                                <option value="">Pilih Unit</option>
                                <?php if (!empty($senarai_unit)): ?>
                                    <?php foreach ($senarai_unit as $unit): ?>
                                        <option value="<?= $unit['id'] ?>"
                                                data-bahagian="<?= $unit['idbahagian'] ?>"
                                                <?= (isset($pengguna['unit_id']) && $pengguna['unit_id'] == $unit['id']) ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($unit['unit']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <option value="" disabled>Tiada data unit</option>
                                <?php endif; ?>
                            </select>
                        </div>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>

                    <?php if ($columns_exist['jawatan_id'] || $columns_exist['gred_id']): ?>
                    <div class="row">
                        <?php if ($columns_exist['jawatan_id']): ?>
                        <div class="col-md-6 mb-3">
                            <label for="jawatan_id" class="form-label">Jawatan</label>
                            <select class="form-select" id="jawatan_id" name="jawatan_id">
                                <option value="">Pilih Jawatan</option>
                                <?php if (!empty($senarai_jawatan)): ?>
                                    <?php foreach ($senarai_jawatan as $jawatan): ?>
                                        <option value="<?= $jawatan['id'] ?>"
                                                <?= (isset($pengguna['jawatan_id']) && $pengguna['jawatan_id'] == $jawatan['id']) ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($jawatan['jawatan']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <option value="" disabled>Tiada data jawatan</option>
                                <?php endif; ?>
                            </select>
                        </div>
                        <?php endif; ?>

                        <?php if ($columns_exist['gred_id']): ?>
                        <div class="col-md-6 mb-3">
                            <label for="gred_id" class="form-label">Gred</label>
                            <select class="form-select" id="gred_id" name="gred_id">
                                <option value="">Pilih Gred</option>
                                <?php if (!empty($senarai_gred)): ?>
                                    <?php foreach ($senarai_gred as $gred): ?>
                                        <option value="<?= $gred['id'] ?>"
                                                <?= (isset($pengguna['gred_id']) && $pengguna['gred_id'] == $gred['id']) ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($gred['gred']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <option value="" disabled>Tiada data gred</option>
                                <?php endif; ?>
                            </select>
                        </div>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="status" class="form-label">Status</label>
                            <input type="text" class="form-control" id="status"
                                   value="<?= ucfirst($pengguna['status']) ?>" readonly>
                        </div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <button type="submit" name="kemaskini_profil" class="btn btn-primary">
                            <i class="bi bi-check-circle me-2"></i>Kemaskini Profil
                        </button>
                    </div>
                </form>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Maklumat Akaun -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-info-circle me-2"></i>Maklumat Akaun</h6>
            </div>
            <div class="card-body">
                <?php if ($pengguna): ?>
                <table class="table table-sm">
                    <tr>
                        <td><strong>No. KP:</strong></td>
                        <td><?= htmlspecialchars($pengguna['nokp']) ?></td>
                    </tr>
                    <tr>
                        <td><strong>Nama:</strong></td>
                        <td><?= htmlspecialchars($pengguna['nama_penuh']) ?></td>
                    </tr>
                    <tr>
                        <td><strong>Bahagian:</strong></td>
                        <td><?= htmlspecialchars($pengguna['bahagian'] ?? 'Tidak ditetapkan') ?></td>
                    </tr>
                    <tr>
                        <td><strong>Unit:</strong></td>
                        <td><?= htmlspecialchars($pengguna['unit'] ?? 'Tidak ditetapkan') ?></td>
                    </tr>
                    <tr>
                        <td><strong>Jawatan:</strong></td>
                        <td><?= htmlspecialchars($pengguna['jawatan'] ?? 'Tidak ditetapkan') ?></td>
                    </tr>
                    <tr>
                        <td><strong>Gred:</strong></td>
                        <td><?= htmlspecialchars($pengguna['gred'] ?? 'Tidak ditetapkan') ?></td>
                    </tr>
                    <tr>
                        <td><strong>Status:</strong></td>
                        <td>
                            <span class="badge <?= ($pengguna['status'] == 'aktif') ? 'bg-success' : 'bg-secondary' ?>">
                                <?= ucfirst($pengguna['status']) ?>
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Tarikh Daftar:</strong></td>
                        <td><?= formatTarikhMasa($pengguna['tarikh_daftar']) ?></td>
                    </tr>
                </table>
                <?php endif; ?>
            </div>
        </div>

        <!-- Pautan Berguna -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-link me-2"></i>Pautan Berguna</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="../Logdaftar/reset_password.php" class="btn btn-outline-primary btn-sm">
                        <i class="bi bi-key me-2"></i>Tukar Kata Laluan
                    </a>
                    <a href="Senarai_tempahan.php" class="btn btn-outline-primary btn-sm">
                        <i class="bi bi-calendar-check me-2"></i>Tempahan Saya
                    </a>
                    <a href="dashboard.php" class="btn btn-outline-primary btn-sm">
                        <i class="bi bi-house me-2"></i>Dashboard
                    </a>
                    <a href="../Logdaftar/log_keluar.php" class="btn btn-outline-danger btn-sm">
                        <i class="bi bi-box-arrow-right me-2"></i>Log Keluar
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Fungsi untuk mengemaskini pilihan unit berdasarkan bahagian yang dipilih
function updateUnitOptions() {
    const bahagianSelect = document.getElementById('bahagian_id');
    const unitSelect = document.getElementById('unit_id');
    const selectedBahagian = bahagianSelect.value;

    // Reset unit dropdown
    const unitOptions = unitSelect.querySelectorAll('option');

    unitOptions.forEach(option => {
        if (option.value === '') {
            option.style.display = 'block'; // Tunjukkan pilihan kosong
        } else {
            const unitBahagian = option.getAttribute('data-bahagian');
            if (selectedBahagian === '' || unitBahagian === selectedBahagian) {
                option.style.display = 'block';
            } else {
                option.style.display = 'none';
                option.selected = false; // Nyahpilih jika disembunyikan
            }
        }
    });

    // Reset unit selection jika bahagian berubah
    if (selectedBahagian === '') {
        unitSelect.value = '';
    }
}

// Panggil fungsi semasa halaman dimuatkan untuk set pilihan awal
document.addEventListener('DOMContentLoaded', function() {
    updateUnitOptions();
});
</script>

<?php require_once '../includes/footer_sistem.php'; ?>
