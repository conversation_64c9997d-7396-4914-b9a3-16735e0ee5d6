<?php
/**
 * Sistem Tempahan Bilik Mesyuarat - Senarai Bilik Baru
 * Fail ini menunjukkan bagaimana sistem boleh menggunakan bilik mesyuarat baru
 */

session_start();

// Sambungan pangkalan data (sesuaikan dengan tetapan anda)
$host = 'localhost';
$dbname = 'sistem_tempahan_bilik'; // Nama pangkalan data yang digunakan sistem
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Ralat sambungan: " . $e->getMessage());
}

// Fungsi untuk mendapatkan senarai bilik (dengan butiran) mengikut bahagian
function getBilikByBahagian($pdo, $bahagian_id = null) {
    if ($bahagian_id) {
        $sql = "SELECT b.*,
                       bh.bahagian AS nama_bahagian,
                       GROUP_CONCAT(DISTINCT p.nama_penuh SEPARATOR ', ') AS penyelaras_nama,
                       GROUP_CONCAT(DISTINCT CONCAT(k.kemudahan, IF(bk.bilangan IS NULL OR bk.bilangan<=1, '', CONCAT(' x', bk.bilangan))) SEPARATOR '|') AS kemudahan_items
                FROM tbilik_mesyuarat b
                LEFT JOIN tbahagian bh ON b.bahagian = bh.id
                LEFT JOIN tbilik_penyelaras bp ON b.id = bp.idbilik_mesyuarat
                LEFT JOIN pengguna p ON bp.idpenyelaras = p.id
                LEFT JOIN tbilik_kemudahan bk ON b.id = bk.idbilik_mesyuarat
                LEFT JOIN tkemudahan k ON bk.idkemudahan = k.id
                WHERE b.bahagian = ?
                GROUP BY b.id
                ORDER BY b.nama_bilik_mesyuarat";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$bahagian_id]);
    } else {
        $sql = "SELECT b.*,
                       bh.bahagian AS nama_bahagian,
                       GROUP_CONCAT(DISTINCT p.nama_penuh SEPARATOR ', ') AS penyelaras_nama,
                       GROUP_CONCAT(DISTINCT CONCAT(k.kemudahan, IF(bk.bilangan IS NULL OR bk.bilangan<=1, '', CONCAT(' x', bk.bilangan))) SEPARATOR '|') AS kemudahan_items
                FROM tbilik_mesyuarat b
                LEFT JOIN tbahagian bh ON b.bahagian = bh.id
                LEFT JOIN tbilik_penyelaras bp ON b.id = bp.idbilik_mesyuarat
                LEFT JOIN pengguna p ON bp.idpenyelaras = p.id
                LEFT JOIN tbilik_kemudahan bk ON b.id = bk.idbilik_mesyuarat
                LEFT JOIN tkemudahan k ON bk.idkemudahan = k.id
                GROUP BY b.id
                ORDER BY b.bahagian, b.nama_bilik_mesyuarat";
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
    }
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Fungsi untuk mendapatkan nama bahagian
function getNamaBahagian($bahagian_id) {
    $bahagian = [
        1 => 'Pentadbiran & Kesihatan Awam',
        2 => 'Perubatan',
        3 => 'Pengurusan',
        4 => 'Pergigian',
        5 => 'Farmasi',
        6 => 'Teknologi Maklumat dan Komunikasi',
        7 => 'Penyelidikan dan Pembangunan',
        8 => 'Latihan dan Pembangunan'
    ];
    return isset($bahagian[$bahagian_id]) ? $bahagian[$bahagian_id] : 'Tidak Diketahui';
}

// Fungsi untuk memeriksa ketersediaan bilik
function checkKetersediaan($pdo, $bilik_id, $tarikh, $masa_mula, $masa_tamat) {
    $sql = "SELECT COUNT(*) FROM ttempahan 
            WHERE idbilik_mesyuarat = ? 
            AND tarikh_tempahan = ? 
            AND ((masa_mula <= ? AND masa_tamat > ?) 
                 OR (masa_mula < ? AND masa_tamat >= ?)
                 OR (masa_mula >= ? AND masa_tamat <= ?))
            AND status != 'DIBATAL'";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([
        $bilik_id, $tarikh, 
        $masa_mula, $masa_mula,
        $masa_tamat, $masa_tamat,
        $masa_mula, $masa_tamat
    ]);
    
    return $stmt->fetchColumn() == 0;
}

?>
<?php $tajuk_halaman = 'Senarai Bilik'; require_once '../includes/header_sistem.php'; ?>

<!-- Mesej Status -->
<?php
$mesej_kejayaan = '';
$mesej_ralat = '';

if (isset($_GET['success'])) {
    switch ($_GET['success']) {
        case 'bilik_deleted':
            $nama_bilik = isset($_GET['name']) ? htmlspecialchars(urldecode($_GET['name'])) : 'bilik';
            $mesej_kejayaan = "Bilik '$nama_bilik' berjaya dihapus.";
            break;
    }
}

if (isset($_GET['error'])) {
    switch ($_GET['error']) {
        case 'access_denied':
            $mesej_ralat = 'Akses ditolak. Hanya admin boleh melakukan tindakan ini.';
            break;
        case 'invalid_id':
            $mesej_ralat = 'ID bilik tidak sah.';
            break;
        case 'bilik_not_found':
            $mesej_ralat = 'Bilik tidak dijumpai.';
            break;
        case 'has_bookings':
            $count = isset($_GET['count']) ? (int)$_GET['count'] : 0;
            $mesej_ralat = "Bilik tidak boleh dihapus kerana masih mempunyai $count tempahan aktif.";
            break;
        case 'delete_failed':
            $mesej_ralat = 'Ralat semasa menghapus bilik.';
            break;
        case 'database_error':
            $mesej_ralat = 'Ralat pangkalan data. Sila cuba lagi.';
            break;
    }
}
?>

<?php if ($mesej_kejayaan): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="bi bi-check-circle me-2"></i><?= $mesej_kejayaan ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($mesej_ralat): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="bi bi-exclamation-triangle me-2"></i><?= $mesej_ralat ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Header dengan butang admin -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <div>
                <h2><i class="bi bi-door-open me-2"></i>Senarai Bilik Mesyuarat</h2>
                <p class="text-muted mb-0">Lihat dan kelola bilik mesyuarat yang tersedia</p>
            </div>
            <?php if (isAdmin()): ?>
                <div>
                    <a href="tambah_bilik.php" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-2"></i>Tambah Bilik Mesyuarat
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row align-items-end g-3">
                    <div class="col-md-6">
                        <label for="bahagian" class="form-label">Tapis mengikut Bahagian</label>
                        <select id="bahagian" class="form-select" onchange="filterBahagian()">
                            <option value="">Semua Bahagian</option>
                            <?php for($i = 1; $i <= 8; $i++): ?>
                                <option value="<?= $i ?>" <?= (($_GET['bahagian'] ?? '') == (string)$i) ? 'selected' : '' ?>>
                                    <?= getNamaBahagian($i) ?>
                                </option>
                            <?php endfor; ?>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Dapatkan parameter bahagian jika ada
$bahagian_filter = isset($_GET['bahagian']) ? (int)$_GET['bahagian'] : null;

if ($bahagian_filter) {
    $bilik_list = getBilikByBahagian($pdo, $bahagian_filter);
    ?>
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="bi bi-door-open me-2"></i><?= getNamaBahagian($bahagian_filter) ?></h5>
            <span class="badge bg-primary"><?= count($bilik_list) ?> bilik</span>
        </div>
        <div class="card-body">
            <div class="row g-3">
                <?php foreach ($bilik_list as $bilik): ?>
                    <div class="col-md-4">
                        <div class="card h-100">
                            <div class="card-body">
                                <h5 class="card-title mb-3"><?= htmlspecialchars($bilik['nama_bilik_mesyuarat']) ?></h5>

                                <div class="row mb-2">
                                    <div class="col-6">
                                        <small class="text-muted">Bahagian:</small><br>
                                        <strong><?= htmlspecialchars($bilik['nama_bahagian'] ?? getNamaBahagian($bilik['bahagian'] ?? 0)) ?></strong>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">Kapasiti:</small><br>
                                        <strong><?= $bilik['kapasiti'] ?> orang</strong>
                                    </div>
                                </div>

                                <div class="mb-2">
                                    <small class="text-muted">Penyelaras Bilik:</small><br>
                                    <strong><?= htmlspecialchars($bilik['penyelaras_nama'] ?: 'Tiada penyelaras') ?></strong>
                                </div>

                                <?php
                                $kemudahan_display = [];
                                if (!empty($bilik['kemudahan_items'])) {
                                    $kemudahan_display = array_filter(explode('|', $bilik['kemudahan_items']));
                                }
                                ?>
                                <div class="mb-2">
                                    <small class="text-muted">Kemudahan Bilik:</small><br>
                                    <?php if (!empty($kemudahan_display)): ?>
                                        <div class="mt-1">
                                            <?php foreach ($kemudahan_display as $k): ?>
                                                <span class="badge bg-success text-white me-1 mb-1">
                                                    <i class="bi bi-check-circle me-1"></i><?= htmlspecialchars($k) ?>
                                                </span>
                                            <?php endforeach; ?>
                                        </div>
                                    <?php else: ?>
                                        <span class="text-muted">Tiada kemudahan tersenarai</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="card-footer bg-transparent">
                                <?php if (isAdmin()): ?>
                                    <div class="d-grid gap-2">
                                        <button class="btn btn-success" onclick="tempahBilik(<?= $bilik['id'] ?>)">
                                            <i class="bi bi-calendar-plus me-2"></i>Tempah Bilik
                                        </button>
                                        <div class="btn-group" role="group">
                                            <a href="edit_bilik.php?id=<?= $bilik['id'] ?>" class="btn btn-warning btn-sm">
                                                <i class="bi bi-pencil me-1"></i>Edit
                                            </a>
                                            <button class="btn btn-danger btn-sm" onclick="hapusBilik(<?= $bilik['id'] ?>, '<?= htmlspecialchars($bilik['nama_bilik_mesyuarat']) ?>')">
                                                <i class="bi bi-trash me-1"></i>Hapus
                                            </button>
                                        </div>
                                    </div>
                                <?php else: ?>
                                    <button class="btn btn-success w-100" onclick="tempahBilik(<?= $bilik['id'] ?>)">
                                        <i class="bi bi-calendar-plus me-2"></i>Tempah Bilik
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
    <?php
} else {
    for ($bahagian_id = 1; $bahagian_id <= 8; $bahagian_id++) {
        $bilik_list = getBilikByBahagian($pdo, $bahagian_id);
        if (!empty($bilik_list)) { ?>
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="bi bi-door-open me-2"></i><?= getNamaBahagian($bahagian_id) ?></h5>
                    <span class="badge bg-primary"><?= count($bilik_list) ?> bilik</span>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <?php foreach ($bilik_list as $bilik): ?>
                            <div class="col-md-4">
                                <div class="card h-100">
                                    <div class="card-body">
                                        <h5 class="card-title mb-3"><?= htmlspecialchars($bilik['nama_bilik_mesyuarat']) ?></h5>

                                        <div class="row mb-2">
                                            <div class="col-6">
                                                <small class="text-muted">Bahagian:</small><br>
                                                <strong><?= htmlspecialchars($bilik['nama_bahagian'] ?? getNamaBahagian($bilik['bahagian'] ?? 0)) ?></strong>
                                            </div>
                                            <div class="col-6">
                                                <small class="text-muted">Kapasiti:</small><br>
                                                <strong><?= $bilik['kapasiti'] ?> orang</strong>
                                            </div>
                                        </div>

                                        <div class="mb-2">
                                            <small class="text-muted">Penyelaras Bilik:</small><br>
                                            <strong><?= htmlspecialchars($bilik['penyelaras_nama'] ?: 'Tiada penyelaras') ?></strong>
                                        </div>

                                        <?php
                                        $kemudahan_display = [];
                                        if (!empty($bilik['kemudahan_items'])) {
                                            $kemudahan_display = array_filter(explode('|', $bilik['kemudahan_items']));
                                        }
                                        ?>
                                        <div class="mb-2">
                                            <small class="text-muted">Kemudahan Bilik:</small><br>
                                            <?php if (!empty($kemudahan_display)): ?>
                                                <div class="mt-1">
                                                    <?php foreach ($kemudahan_display as $k): ?>
                                                        <span class="badge bg-success text-white me-1 mb-1">
                                                            <i class="bi bi-check-circle me-1"></i><?= htmlspecialchars($k) ?>
                                                        </span>
                                                    <?php endforeach; ?>
                                                </div>
                                            <?php else: ?>
                                                <span class="text-muted">Tiada kemudahan tersenarai</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="card-footer bg-transparent">
                                        <?php if (isAdmin()): ?>
                                            <div class="d-grid gap-2">
                                                <button class="btn btn-success" onclick="tempahBilik(<?= $bilik['id'] ?>)">
                                                    <i class="bi bi-calendar-plus me-2"></i>Tempah Bilik
                                                </button>
                                                <div class="btn-group" role="group">
                                                    <a href="edit_bilik.php?id=<?= $bilik['id'] ?>" class="btn btn-warning btn-sm">
                                                        <i class="bi bi-pencil me-1"></i>Edit
                                                    </a>
                                                    <button class="btn btn-danger btn-sm" onclick="hapusBilik(<?= $bilik['id'] ?>, '<?= htmlspecialchars($bilik['nama_bilik_mesyuarat']) ?>')">
                                                        <i class="bi bi-trash me-1"></i>Hapus
                                                    </button>
                                                </div>
                                            </div>
                                        <?php else: ?>
                                            <button class="btn btn-success w-100" onclick="tempahBilik(<?= $bilik['id'] ?>)">
                                                <i class="bi bi-calendar-plus me-2"></i>Tempah Bilik
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        <?php }
    }
}
?>

<!-- Modal Hapus Bilik -->
<div class="modal fade" id="hapusBilikModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Hapus Bilik Mesyuarat</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Adakah anda pasti ingin menghapus bilik <strong id="hapus_nama_bilik"></strong>?</p>
                <p class="text-danger">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    <strong>Amaran:</strong> Tindakan ini akan menghapus bilik dan semua tempahan yang berkaitan. Tindakan ini tidak boleh dibatalkan.
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                <button type="button" class="btn btn-danger" id="confirm_hapus_bilik">Hapus Bilik</button>
            </div>
        </div>
    </div>
</div>

<script>
function filterBahagian() {
    const bahagian = document.getElementById('bahagian').value;
    const base = window.location.pathname;
    window.location.href = bahagian ? `${base}?bahagian=${bahagian}` : base;
}

function tempahBilik(bilikId) {
    window.location.href = '../BuatTempahan/tempah_bilik.php?bilik_id=' + bilikId;
}

function hapusBilik(bilikId, namaBilik) {
    document.getElementById('hapus_nama_bilik').textContent = namaBilik;

    // Set event listener untuk butang confirm
    document.getElementById('confirm_hapus_bilik').onclick = function() {
        // Redirect ke halaman hapus dengan parameter
        window.location.href = 'hapus_bilik.php?id=' + bilikId;
    };

    // Show modal
    new bootstrap.Modal(document.getElementById('hapusBilikModal')).show();
}
</script>

<?php require_once '../includes/footer_sistem.php'; ?>
