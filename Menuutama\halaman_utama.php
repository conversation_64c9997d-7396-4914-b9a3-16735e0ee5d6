<?php
/**
 * Halaman Utama
 * Sistem Tempahan Bilik Mesyuarat
 */

$tajuk_halaman = 'Halaman Utama';
require_once '../config/sistem_config.php';

// Jika pengguna sudah log masuk, arahkan ke dashboard
if (semakLogin()) {
    arahkanKe(URL_SISTEM . '/dashboard.php');
}

// Dapatkan statistik sistem untuk paparan awam
try {
    $sql_total_bilik = "SELECT COUNT(*) as jumlah FROM bilik_mesyuarat WHERE status = 'tersedia'";
    $total_bilik = $db->fetch($sql_total_bilik)['jumlah'] ?? 0;
    
    $sql_tempahan_hari_ini = "SELECT COUNT(*) as jumlah FROM tempahan WHERE tarikh_tempahan = CURDATE()";
    $tempahan_hari_ini = $db->fetch($sql_tempahan_hari_ini)['jumlah'] ?? 0;
    
    $sql_pengguna_aktif = "SELECT COUNT(*) as jumlah FROM pengguna WHERE status = 'aktif'";
    $pengguna_aktif = $db->fetch($sql_pengguna_aktif)['jumlah'] ?? 0;
    
    // Dapatkan beberapa bilik popular untuk paparan
    $sql_bilik_popular = "SELECT b.*, COUNT(t.id) as jumlah_tempahan 
                          FROM bilik_mesyuarat b 
                          LEFT JOIN tempahan t ON b.id = t.bilik_id 
                          AND t.tarikh_tempahan >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
                          WHERE b.status = 'tersedia'
                          GROUP BY b.id 
                          ORDER BY jumlah_tempahan DESC 
                          LIMIT 6";
    $bilik_popular = $db->fetchAll($sql_bilik_popular);
    
} catch (Exception $e) {
    $total_bilik = 0;
    $tempahan_hari_ini = 0;
    $pengguna_aktif = 0;
    $bilik_popular = [];
}

require_once '../includes/header_sistem.php';
?>

<!-- Hero Section -->
<div class="bg-primary text-white py-5 mb-5" style="margin-top: -2rem;">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold mb-3">Sistem Tempahan Bilik Mesyuarat</h1>
                <p class="lead mb-4">
                    Platform digital yang mudah dan cekap untuk menguruskan tempahan bilik mesyuarat. 
                    Tempah bilik dengan mudah, pantau ketersediaan secara real-time, dan uruskan jadual mesyuarat anda.
                </p>
                <div class="d-flex gap-3">
                    <a href="../Logdaftar/log_masuk.php" class="btn btn-light btn-lg">
                        <i class="bi bi-box-arrow-in-right me-2"></i>Log Masuk
                    </a>
                    <a href="../Logdaftar/daftar.php" class="btn btn-outline-light btn-lg">
                        <i class="bi bi-person-plus me-2"></i>Daftar Akaun
                    </a>
                </div>
            </div>
            <div class="col-lg-6 text-center">
                <i class="bi bi-building display-1 opacity-75"></i>
            </div>
        </div>
    </div>
</div>

<!-- Statistik Sistem -->
<div class="row mb-5">
    <div class="col-12 text-center mb-4">
        <h2 class="h3">Statistik Sistem</h2>
        <p class="text-muted">Maklumat terkini tentang penggunaan sistem</p>
    </div>
    
    <div class="col-md-4 mb-3">
        <div class="card text-center border-primary">
            <div class="card-body">
                <i class="bi bi-door-open fs-1 text-primary mb-3"></i>
                <h3 class="text-primary"><?= $total_bilik ?></h3>
                <p class="mb-0">Bilik Tersedia</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-3">
        <div class="card text-center border-success">
            <div class="card-body">
                <i class="bi bi-calendar-event fs-1 text-success mb-3"></i>
                <h3 class="text-success"><?= $tempahan_hari_ini ?></h3>
                <p class="mb-0">Tempahan Hari Ini</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-3">
        <div class="card text-center border-info">
            <div class="card-body">
                <i class="bi bi-people fs-1 text-info mb-3"></i>
                <h3 class="text-info"><?= $pengguna_aktif ?></h3>
                <p class="mb-0">Pengguna Aktif</p>
            </div>
        </div>
    </div>
</div>

<!-- Ciri-ciri Utama -->
<div class="row mb-5">
    <div class="col-12 text-center mb-4">
        <h2 class="h3">Ciri-ciri Utama</h2>
        <p class="text-muted">Kemudahan yang disediakan oleh sistem</p>
    </div>
    
    <div class="col-md-4 mb-4">
        <div class="card h-100 text-center">
            <div class="card-body">
                <i class="bi bi-calendar-plus fs-1 text-primary mb-3"></i>
                <h5 class="card-title">Tempahan Mudah</h5>
                <p class="card-text">
                    Buat tempahan bilik dengan mudah melalui antara muka yang mesra pengguna. 
                    Pilih tarikh, masa, dan bilik yang sesuai dengan keperluan anda.
                </p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-4">
        <div class="card h-100 text-center">
            <div class="card-body">
                <i class="bi bi-clock-history fs-1 text-success mb-3"></i>
                <h5 class="card-title">Ketersediaan Real-time</h5>
                <p class="card-text">
                    Semak ketersediaan bilik secara real-time. Sistem akan menunjukkan 
                    masa yang tersedia dan tempahan yang telah dibuat.
                </p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-4">
        <div class="card h-100 text-center">
            <div class="card-body">
                <i class="bi bi-bell fs-1 text-warning mb-3"></i>
                <h5 class="card-title">Notifikasi Automatik</h5>
                <p class="card-text">
                    Terima notifikasi automatik untuk pengesahan tempahan, peringatan mesyuarat, 
                    dan kemaskini status tempahan.
                </p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-4">
        <div class="card h-100 text-center">
            <div class="card-body">
                <i class="bi bi-graph-up fs-1 text-info mb-3"></i>
                <h5 class="card-title">Laporan & Analitik</h5>
                <p class="card-text">
                    Akses laporan penggunaan bilik, statistik tempahan, dan analitik 
                    untuk membantu dalam perancangan dan pengurusan.
                </p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-4">
        <div class="card h-100 text-center">
            <div class="card-body">
                <i class="bi bi-shield-check fs-1 text-danger mb-3"></i>
                <h5 class="card-title">Keselamatan Terjamin</h5>
                <p class="card-text">
                    Sistem dilengkapi dengan ciri keselamatan yang kukuh untuk melindungi 
                    data pengguna dan maklumat tempahan.
                </p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-4">
        <div class="card h-100 text-center">
            <div class="card-body">
                <i class="bi bi-phone fs-1 text-secondary mb-3"></i>
                <h5 class="card-title">Responsif & Mobile</h5>
                <p class="card-text">
                    Akses sistem dari mana-mana peranti - desktop, tablet, atau telefon pintar. 
                    Antara muka yang responsif untuk kemudahan penggunaan.
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Bilik Popular -->
<?php if (!empty($bilik_popular)): ?>
<div class="row mb-5">
    <div class="col-12 text-center mb-4">
        <h2 class="h3">Bilik Mesyuarat Popular</h2>
        <p class="text-muted">Bilik yang kerap digunakan dalam tempoh 30 hari lepas</p>
    </div>
    
    <?php foreach (array_slice($bilik_popular, 0, 6) as $bilik): ?>
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card h-100">
                <?php if (!empty($bilik['gambar_url'])): ?>
                    <img src="<?= htmlspecialchars($bilik['gambar_url']) ?>" 
                         class="card-img-top" style="height: 200px; object-fit: cover;" 
                         alt="<?= htmlspecialchars($bilik['nama_bilik']) ?>">
                <?php else: ?>
                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center" 
                         style="height: 200px;">
                        <i class="bi bi-door-open fs-1 text-muted"></i>
                    </div>
                <?php endif; ?>
                
                <div class="card-body">
                    <h5 class="card-title"><?= htmlspecialchars($bilik['nama_bilik']) ?></h5>
                    <p class="card-text">
                        <small class="text-muted">
                            <i class="bi bi-people me-1"></i>Kapasiti: <?= $bilik['kapasiti'] ?> orang<br>
                            <i class="bi bi-geo-alt me-1"></i><?= htmlspecialchars($bilik['lokasi'] ?? 'Lokasi tidak dinyatakan') ?><br>
                            <i class="bi bi-star me-1"></i><?= $bilik['jumlah_tempahan'] ?> tempahan (30 hari)
                        </small>
                    </p>
                </div>
            </div>
        </div>
    <?php endforeach; ?>
</div>
<?php endif; ?>

<!-- Cara Menggunakan -->
<div class="bg-light py-5 mb-5" style="margin-left: -15px; margin-right: -15px;">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-4">
                <h2 class="h3">Cara Menggunakan Sistem</h2>
                <p class="text-muted">Langkah mudah untuk membuat tempahan bilik mesyuarat</p>
            </div>
            
            <div class="col-md-3 text-center mb-4">
                <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                     style="width: 60px; height: 60px;">
                    <span class="fs-4 fw-bold">1</span>
                </div>
                <h5>Daftar Akaun</h5>
                <p class="small text-muted">Daftar akaun baru atau log masuk dengan akaun sedia ada</p>
            </div>
            
            <div class="col-md-3 text-center mb-4">
                <div class="bg-success text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                     style="width: 60px; height: 60px;">
                    <span class="fs-4 fw-bold">2</span>
                </div>
                <h5>Pilih Bilik</h5>
                <p class="small text-muted">Lihat senarai bilik tersedia dan pilih yang sesuai</p>
            </div>
            
            <div class="col-md-3 text-center mb-4">
                <div class="bg-warning text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                     style="width: 60px; height: 60px;">
                    <span class="fs-4 fw-bold">3</span>
                </div>
                <h5>Buat Tempahan</h5>
                <p class="small text-muted">Isi borang tempahan dengan maklumat yang diperlukan</p>
            </div>
            
            <div class="col-md-3 text-center mb-4">
                <div class="bg-info text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                     style="width: 60px; height: 60px;">
                    <span class="fs-4 fw-bold">4</span>
                </div>
                <h5>Terima Pengesahan</h5>
                <p class="small text-muted">Tunggu kelulusan dan terima notifikasi pengesahan</p>
            </div>
        </div>
    </div>
</div>

<!-- Call to Action -->
<div class="text-center py-5">
    <h2 class="h3 mb-3">Mula Menggunakan Sistem Hari Ini</h2>
    <p class="lead text-muted mb-4">
        Sertai ribuan pengguna yang telah mempercayai sistem kami untuk menguruskan tempahan bilik mesyuarat
    </p>
    <div class="d-flex justify-content-center gap-3">
        <a href="../Logdaftar/daftar.php" class="btn btn-primary btn-lg">
            <i class="bi bi-person-plus me-2"></i>Daftar Sekarang
        </a>
        <a href="../Logdaftar/log_masuk.php" class="btn btn-outline-primary btn-lg">
            <i class="bi bi-box-arrow-in-right me-2"></i>Log Masuk
        </a>
    </div>
</div>

<?php require_once '../includes/footer_sistem.php'; ?>
