<?php
/**
 * Test script untuk masukkan data tempahan untuk testing
 * Jalankan sekali sahaja untuk test data
 */

session_start();

// Konfigurasi database
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'sistem_tempahan_bilik';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Test Data Insertion untuk Tempahan</h2>";
    
    // Semak jika ada pengguna
    $check_users = $pdo->query("SELECT id, nama_penuh FROM pengguna LIMIT 5");
    $users = $check_users->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($users)) {
        echo "<p style='color: red;'>Tiada pengguna dalam sistem. Sila daftar pengguna dahulu.</p>";
        exit;
    }
    
    echo "<h3>Pengguna yang ada:</h3>";
    foreach ($users as $user) {
        echo "<li>ID: {$user['id']} - {$user['nama_penuh']}</li>";
    }
    
    // Semak jika ada bilik
    $check_rooms = $pdo->query("SELECT id, nama_bilik_mesyuarat FROM tbilik_mesyuarat LIMIT 5");
    $rooms = $check_rooms->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($rooms)) {
        echo "<p style='color: red;'>Tiada bilik dalam sistem. Sila setup bilik dahulu.</p>";
        exit;
    }
    
    echo "<h3>Bilik yang ada:</h3>";
    foreach ($rooms as $room) {
        echo "<li>ID: {$room['id']} - {$room['nama_bilik_mesyuarat']}</li>";
    }
    
    // Semak struktur jadual ttempahan
    echo "<h3>Struktur jadual ttempahan:</h3>";
    $columns = $pdo->query("SHOW COLUMNS FROM ttempahan");
    foreach ($columns as $col) {
        echo "<li>{$col['Field']} - {$col['Type']}</li>";
    }
    
    // Semak data sedia ada
    $existing = $pdo->query("SELECT COUNT(*) as total FROM ttempahan")->fetch();
    echo "<h3>Data sedia ada: {$existing['total']} tempahan</h3>";
    
    // Masukkan data test jika belum ada
    if ($existing['total'] == 0) {
        echo "<h3>Memasukkan data test...</h3>";
        
        $test_data = [
            [
                'idpemohon' => $users[0]['id'],
                'tarikh_mohon' => date('Y-m-d H:i:s'),
                'tajuk_mesyuarat' => 'Mesyuarat Test 1',
                'idbilik_mesyuarat' => $rooms[0]['id'],
                'tarikh_mula' => date('Y-m-d H:i:s', strtotime('+1 day 09:00')),
                'tarikh_tamat' => date('Y-m-d H:i:s', strtotime('+1 day 11:00')),
                'sesi' => 1,
                'bilangan_peserta' => 10,
                'pengerusi' => 'Ahmad bin Ali',
                'tahun' => date('Y'),
                'kelulusan' => 'MENUNGGU',
                'batal_tempahan' => 'TIDAK'
            ],
            [
                'idpemohon' => $users[0]['id'],
                'tarikh_mohon' => date('Y-m-d H:i:s', strtotime('-1 day')),
                'tajuk_mesyuarat' => 'Mesyuarat Test 2',
                'idbilik_mesyuarat' => $rooms[0]['id'],
                'tarikh_mula' => date('Y-m-d H:i:s', strtotime('+2 days 14:00')),
                'tarikh_tamat' => date('Y-m-d H:i:s', strtotime('+2 days 16:00')),
                'sesi' => 2,
                'bilangan_peserta' => 15,
                'pengerusi' => 'Siti binti Hassan',
                'tahun' => date('Y'),
                'kelulusan' => 'LULUS',
                'batal_tempahan' => 'TIDAK'
            ]
        ];
        
        $sql = "INSERT INTO ttempahan (idpemohon, tarikh_mohon, tajuk_mesyuarat, idbilik_mesyuarat, 
                tarikh_mula, tarikh_tamat, sesi, bilangan_peserta, pengerusi, tahun, kelulusan, batal_tempahan, tarikh_kemaskini) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
        
        $stmt = $pdo->prepare($sql);
        
        foreach ($test_data as $data) {
            $result = $stmt->execute([
                $data['idpemohon'], $data['tarikh_mohon'], $data['tajuk_mesyuarat'], 
                $data['idbilik_mesyuarat'], $data['tarikh_mula'], $data['tarikh_tamat'],
                $data['sesi'], $data['bilangan_peserta'], $data['pengerusi'],
                $data['tahun'], $data['kelulusan'], $data['batal_tempahan']
            ]);
            
            if ($result) {
                echo "<p style='color: green;'>✓ Tempahan '{$data['tajuk_mesyuarat']}' berjaya dimasukkan</p>";
            }
        }
        
        echo "<p><strong>Data test telah dimasukkan. Sila semak halaman Senarai Tempahan.</strong></p>";
    } else {
        echo "<p>Data sudah ada. Tidak perlu masukkan data test.</p>";
    }
    
    // Paparkan data terkini
    echo "<h3>Data tempahan terkini:</h3>";
    $recent = $pdo->query("SELECT t.*, p.nama_penuh, b.nama_bilik_mesyuarat 
                          FROM ttempahan t 
                          LEFT JOIN pengguna p ON t.idpemohon = p.id 
                          LEFT JOIN tbilik_mesyuarat b ON t.idbilik_mesyuarat = b.id 
                          ORDER BY t.id DESC LIMIT 5");
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Pemohon</th><th>Tajuk</th><th>Bilik</th><th>Status</th><th>Tarikh Mula</th></tr>";
    
    foreach ($recent as $row) {
        echo "<tr>";
        echo "<td>{$row['id']}</td>";
        echo "<td>{$row['nama_penuh']}</td>";
        echo "<td>{$row['tajuk_mesyuarat']}</td>";
        echo "<td>{$row['nama_bilik_mesyuarat']}</td>";
        echo "<td>{$row['kelulusan']}</td>";
        echo "<td>{$row['tarikh_mula']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Ralat: " . $e->getMessage() . "</p>";
}
?>

<p><a href="Menuutama/Senarai_tempahan.php">← Kembali ke Senarai Tempahan</a></p>
