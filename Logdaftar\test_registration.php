<?php
/**
 * Script untuk menguji sistem pendaftaran
 * Sistem Tempahan Bilik Mesyuarat
 */

require_once '../config/sistem_config.php';

echo "<!DOCTYPE html>
<html lang='ms'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Ujian Sistem Pendaftaran</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>";

echo "<h1>Ujian Sistem Pendaftaran</h1>";

try {
    $pdo = $db->getConnection();
    echo "<p class='success'>✓ Sambungan pangkalan data berjaya</p>";
    
    // Test 1: Check table structure
    echo "<h2>1. Struktur Jadual Pengguna</h2>";
    $columns_result = $db->query("SHOW COLUMNS FROM pengguna");
    $columns = $columns_result->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table>";
    echo "<tr><th>Medan</th><th>Jenis</th><th>Null</th><th>Kunci</th><th>Lalai</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Test 2: Check required lookup tables
    echo "<h2>2. Jadual Rujukan</h2>";
    $lookup_tables = ['tbahagian', 'tunit', 'tjawatan', 'tgred'];
    
    foreach ($lookup_tables as $table) {
        try {
            $count = $pdo->query("SELECT COUNT(*) FROM $table")->fetchColumn();
            if ($count > 0) {
                echo "<p class='success'>✓ $table: $count rekod</p>";
            } else {
                echo "<p class='warning'>⚠ $table: Tiada data</p>";
            }
        } catch (Exception $e) {
            echo "<p class='error'>✗ $table: Jadual tidak wujud</p>";
        }
    }
    
    // Test 3: Simulate registration data preparation
    echo "<h2>3. Simulasi Penyediaan Data Pendaftaran</h2>";
    
    // Get table structure to determine available columns
    $columns_result = $db->query("SHOW COLUMNS FROM pengguna");
    $columns = $columns_result->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<p class='info'>Kolom yang tersedia: " . implode(', ', $columns) . "</p>";
    
    // Build SQL based on available columns (same logic as in daftar.php)
    $base_columns = ['nokp', 'kata_laluan', 'nama_penuh', 'emel', 'no_telefon'];
    $optional_columns = ['bahagian_id', 'unit_id', 'jawatan_id', 'gred_id'];
    $role_columns = ['peranan', 'profile_id'];
    
    $insert_columns = $base_columns;
    $insert_values = ['?', '?', '?', '?', '?'];
    
    // Add optional columns if they exist
    foreach ($optional_columns as $col) {
        if (in_array($col, $columns)) {
            $insert_columns[] = $col;
            $insert_values[] = '?';
        }
    }
    
    // Add role column if it exists
    if (in_array('peranan', $columns)) {
        $insert_columns[] = 'peranan';
        $insert_values[] = '?';
        echo "<p class='success'>✓ Menggunakan kolom 'peranan'</p>";
    } elseif (in_array('profile_id', $columns)) {
        $insert_columns[] = 'profile_id';
        $insert_values[] = '?';
        echo "<p class='success'>✓ Menggunakan kolom 'profile_id'</p>";
    } else {
        echo "<p class='warning'>⚠ Tiada kolom peranan atau profile_id</p>";
    }
    
    $sql_insert = "INSERT INTO pengguna (" . implode(', ', $insert_columns) . ") 
                   VALUES (" . implode(', ', $insert_values) . ")";
    
    echo "<p class='info'>SQL yang akan digunakan:</p>";
    echo "<pre>" . htmlspecialchars($sql_insert) . "</pre>";
    
    // Test 4: Check for existing test user
    echo "<h2>4. Pemeriksaan Pengguna Ujian</h2>";
    $test_nokp = '999999999999';
    $test_email = '<EMAIL>';
    
    $existing = $db->fetch("SELECT id, nokp, nama_penuh, emel FROM pengguna WHERE nokp = ? OR emel = ?", 
                          [$test_nokp, $test_email]);
    
    if ($existing) {
        echo "<p class='info'>Pengguna ujian sudah wujud:</p>";
        echo "<pre>" . print_r($existing, true) . "</pre>";
    } else {
        echo "<p class='success'>✓ Tiada konflik dengan data ujian</p>";
    }
    
    // Test 5: Sample dropdown data
    echo "<h2>5. Data Dropdown</h2>";
    
    try {
        $bahagian = $db->fetchAll("SELECT id, bahagian FROM tbahagian ORDER BY bahagian LIMIT 3");
        echo "<p class='success'>✓ Bahagian: " . count($bahagian) . " rekod</p>";
        if (!empty($bahagian)) {
            echo "<pre>" . print_r($bahagian, true) . "</pre>";
        }
    } catch (Exception $e) {
        echo "<p class='error'>✗ Ralat bahagian: " . $e->getMessage() . "</p>";
    }
    
    try {
        $jawatan = $db->fetchAll("SELECT id, jawatan FROM tjawatan ORDER BY jawatan LIMIT 3");
        echo "<p class='success'>✓ Jawatan: " . count($jawatan) . " rekod</p>";
        if (!empty($jawatan)) {
            echo "<pre>" . print_r($jawatan, true) . "</pre>";
        }
    } catch (Exception $e) {
        echo "<p class='error'>✗ Ralat jawatan: " . $e->getMessage() . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>✗ Ralat utama: " . $e->getMessage() . "</p>";
}

echo "<h2>Kesimpulan</h2>";
echo "<p>Jika semua ujian menunjukkan ✓, sistem pendaftaran sepatutnya berfungsi dengan baik.</p>";
echo "<p>Jika terdapat ✗ atau ⚠, sila jalankan <a href='init_database.php'>init_database.php</a> terlebih dahulu.</p>";

echo "<p><a href='daftar.php'>← Cuba pendaftaran sekarang</a></p>";

echo "</body></html>";
?>
