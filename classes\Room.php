<?php
/**
 * Room Class
 * Handles meeting room management
 */

require_once __DIR__ . '/../config/config.php';

class Room {
    private $db;

    public function __construct() {
        global $db;
        $this->db = $db;
    }

    public function getAllRooms($active_only = true) {
        $sql = "SELECT * FROM meeting_rooms";
        if ($active_only) {
            $sql .= " WHERE is_active = 1";
        }
        $sql .= " ORDER BY room_name";
        
        return $this->db->fetchAll($sql);
    }

    public function getRoomById($id) {
        return $this->db->fetch(
            "SELECT * FROM meeting_rooms WHERE id = ?",
            [$id]
        );
    }

    public function createRoom($data) {
        try {
            $sql = "INSERT INTO meeting_rooms (room_name, capacity, location, description, facilities, hourly_rate, image_url) 
                    VALUES (?, ?, ?, ?, ?, ?, ?)";
            
            $facilities = is_array($data['facilities']) ? json_encode($data['facilities']) : $data['facilities'];
            
            $this->db->query($sql, [
                $data['room_name'],
                $data['capacity'],
                $data['location'],
                $data['description'],
                $facilities,
                $data['hourly_rate'],
                $data['image_url'] ?? null
            ]);

            return ['success' => true, 'message' => 'Room created successfully'];
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Failed to create room: ' . $e->getMessage()];
        }
    }

    public function updateRoom($id, $data) {
        try {
            $sql = "UPDATE meeting_rooms SET room_name = ?, capacity = ?, location = ?, 
                    description = ?, facilities = ?, hourly_rate = ?, image_url = ? WHERE id = ?";
            
            $facilities = is_array($data['facilities']) ? json_encode($data['facilities']) : $data['facilities'];
            
            $this->db->query($sql, [
                $data['room_name'],
                $data['capacity'],
                $data['location'],
                $data['description'],
                $facilities,
                $data['hourly_rate'],
                $data['image_url'] ?? null,
                $id
            ]);

            return ['success' => true, 'message' => 'Room updated successfully'];
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Failed to update room: ' . $e->getMessage()];
        }
    }

    public function toggleRoomStatus($id) {
        try {
            $this->db->query(
                "UPDATE meeting_rooms SET is_active = NOT is_active WHERE id = ?",
                [$id]
            );
            return ['success' => true, 'message' => 'Room status updated successfully'];
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Failed to update room status: ' . $e->getMessage()];
        }
    }

    public function deleteRoom($id) {
        try {
            // Check if room has any bookings
            $bookings = $this->db->fetch(
                "SELECT COUNT(*) as count FROM bookings WHERE room_id = ?",
                [$id]
            );

            if ($bookings['count'] > 0) {
                return ['success' => false, 'message' => 'Cannot delete room with existing bookings'];
            }

            $this->db->query("DELETE FROM meeting_rooms WHERE id = ?", [$id]);
            return ['success' => true, 'message' => 'Room deleted successfully'];
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Failed to delete room: ' . $e->getMessage()];
        }
    }

    public function getRoomAvailability($room_id, $date, $start_time = null, $end_time = null) {
        $sql = "SELECT * FROM bookings WHERE room_id = ? AND booking_date = ? AND status != 'cancelled'";
        $params = [$room_id, $date];

        if ($start_time && $end_time) {
            $sql .= " AND ((start_time < ? AND end_time > ?) OR (start_time < ? AND end_time > ?) OR (start_time >= ? AND end_time <= ?))";
            $params = array_merge($params, [$end_time, $start_time, $start_time, $end_time, $start_time, $end_time]);
        }

        return $this->db->fetchAll($sql, $params);
    }

    public function getAvailableRooms($date, $start_time, $end_time, $capacity = null) {
        $sql = "SELECT r.* FROM meeting_rooms r 
                WHERE r.is_active = 1";
        
        if ($capacity) {
            $sql .= " AND r.capacity >= ?";
            $params[] = $capacity;
        }
        
        $sql .= " AND r.id NOT IN (
                    SELECT DISTINCT room_id FROM bookings 
                    WHERE booking_date = ? 
                    AND status != 'cancelled'
                    AND ((start_time < ? AND end_time > ?) 
                         OR (start_time < ? AND end_time > ?) 
                         OR (start_time >= ? AND end_time <= ?))
                )";
        
        $params = array_merge($params ?? [], [$date, $end_time, $start_time, $start_time, $end_time, $start_time, $end_time]);
        
        return $this->db->fetchAll($sql, $params);
    }

    public function getFacilities($room_id) {
        $room = $this->getRoomById($room_id);
        if ($room && $room['facilities']) {
            return json_decode($room['facilities'], true) ?: [];
        }
        return [];
    }

    public function searchRooms($search_term, $capacity = null, $location = null) {
        $sql = "SELECT * FROM meeting_rooms WHERE is_active = 1";
        $params = [];

        if ($search_term) {
            $sql .= " AND (room_name LIKE ? OR description LIKE ?)";
            $params[] = "%$search_term%";
            $params[] = "%$search_term%";
        }

        if ($capacity) {
            $sql .= " AND capacity >= ?";
            $params[] = $capacity;
        }

        if ($location) {
            $sql .= " AND location LIKE ?";
            $params[] = "%$location%";
        }

        $sql .= " ORDER BY room_name";

        return $this->db->fetchAll($sql, $params);
    }

    public function getRoomStats($room_id) {
        $stats = [];
        
        // Total bookings
        $stats['total_bookings'] = $this->db->fetch(
            "SELECT COUNT(*) as count FROM bookings WHERE room_id = ?",
            [$room_id]
        )['count'];

        // This month bookings
        $stats['month_bookings'] = $this->db->fetch(
            "SELECT COUNT(*) as count FROM bookings WHERE room_id = ? AND MONTH(booking_date) = MONTH(CURDATE()) AND YEAR(booking_date) = YEAR(CURDATE())",
            [$room_id]
        )['count'];

        // Total revenue
        $stats['total_revenue'] = $this->db->fetch(
            "SELECT SUM(total_cost) as revenue FROM bookings WHERE room_id = ? AND status = 'completed'",
            [$room_id]
        )['revenue'] ?? 0;

        // Average rating (if you implement rating system)
        $stats['average_rating'] = 0;

        return $stats;
    }
}
?>
