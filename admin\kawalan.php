<?php
/**
 * <PERSON><PERSON> Kawalan Admin
 * Sistem Tempahan Bilik Mesyuarat
 */

$tajuk_halaman = 'Kawalan Admin';
require_once '../config/sistem_config.php';

// Semak login dan peranan admin
perluLogin();
if (!isset($_SESSION['profile_id']) || $_SESSION['profile_id'] != 2 && $_SESSION['profile_id'] != 3) {
    header('Location: ../Menuutama/dashboard.php');
    exit;
}

require_once '../includes/header_sistem.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="bi bi-gear-fill me-2"></i>Kawalan Admin</h2>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="../Menuutama/dashboard.php">Dashboard</a></li>
                        <li class="breadcrumb-item active"><PERSON><PERSON>n</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="alert alert-info">
                <i class="bi bi-info-circle me-2"></i>
                <strong>Kawalan Admin</strong> - Kelola data asas sistem seperti unit, bahagian dan kemudahan bilik mesyuarat.
                <div class="mt-2">
                    <a href="periksa_database.php" class="btn btn-sm btn-outline-info">
                        <i class="bi bi-database me-1"></i>Periksa Database
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Menu Kawalan -->
    <div class="row g-4">
        <?php if ($_SESSION['profile_id'] == 2): // Hanya admin boleh akses unit dan bahagian ?>
        <!-- Kawalan Unit -->
        <div class="col-lg-4 col-md-6">
            <div class="card h-100 border-primary">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="bi bi-building fs-1 text-primary"></i>
                    </div>
                    <h5 class="card-title">Kawalan Unit</h5>
                    <p class="card-text">
                        Kelola data unit dalam organisasi. Tambah, edit atau hapus unit yang berkaitan dengan bahagian.
                    </p>
                    <div class="d-grid gap-2">
                        <a href="kawalan_unit.php" class="btn btn-primary">
                            <i class="bi bi-gear me-2"></i>Kelola Unit
                        </a>
                        <small class="text-muted">
                            <i class="bi bi-info-circle me-1"></i>
                            Unit dikaitkan dengan bahagian
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Kawalan Bahagian -->
        <div class="col-lg-4 col-md-6">
            <div class="card h-100 border-success">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="bi bi-diagram-3 fs-1 text-success"></i>
                    </div>
                    <h5 class="card-title">Kawalan Bahagian</h5>
                    <p class="card-text">
                        Kelola data bahagian dalam organisasi. Tambah, edit atau hapus bahagian yang ada.
                    </p>
                    <div class="d-grid gap-2">
                        <a href="kawalan_bahagian.php" class="btn btn-success">
                            <i class="bi bi-gear me-2"></i>Kelola Bahagian
                        </a>
                        <small class="text-muted">
                            <i class="bi bi-info-circle me-1"></i>
                            Bahagian induk untuk unit
                        </small>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Kawalan Kemudahan Bilik Mesyuarat -->
        <div class="col-lg-4 col-md-6">
            <div class="card h-100 border-warning">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="bi bi-tools fs-1 text-warning"></i>
                    </div>
                    <h5 class="card-title">Kawalan Kemudahan</h5>
                    <p class="card-text">
                        Kelola kemudahan bilik mesyuarat. Tambah, edit atau hapus kemudahan yang tersedia.
                    </p>
                    <div class="d-grid gap-2">
                        <a href="kawalan_kemudahan.php" class="btn btn-warning">
                            <i class="bi bi-gear me-2"></i>Kelola Kemudahan
                        </a>
                        <small class="text-muted">
                            <i class="bi bi-info-circle me-1"></i>
                            Kemudahan untuk bilik mesyuarat
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistik Ringkas -->
    <div class="row mt-5">
        <div class="col-12">
            <h4><i class="bi bi-bar-chart me-2"></i>Statistik Ringkas</h4>
        </div>
    </div>

    <div class="row g-3">
        <?php
        try {
            // Dapatkan jumlah bilik mesyuarat
            $jumlah_bilik = $db->fetch("SELECT COUNT(*) as jumlah FROM tbilik_mesyuarat")['jumlah'] ?? 0;
            
            // Hanya admin boleh lihat statistik unit dan bahagian
            if ($_SESSION['profile_id'] == 2) {
                $jumlah_unit = $db->fetch("SELECT COUNT(*) as jumlah FROM tunit")['jumlah'] ?? 0;
                $jumlah_bahagian = $db->fetch("SELECT COUNT(*) as jumlah FROM tbahagian")['jumlah'] ?? 0;
            }
        } catch (Exception $e) {
            $jumlah_bilik = 0;
            if ($_SESSION['profile_id'] == 2) {
                $jumlah_unit = 0;
                $jumlah_bahagian = 0;
            }
        }
        ?>
        
        <?php if ($_SESSION['profile_id'] == 2): ?>
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Jumlah Unit</h6>
                            <h3><?= $jumlah_unit ?></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-building fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Jumlah Bahagian</h6>
                            <h3><?= $jumlah_bahagian ?></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-diagram-3 fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Jumlah Bilik</h6>
                            <h3><?= $jumlah_bilik ?></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-door-open fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


