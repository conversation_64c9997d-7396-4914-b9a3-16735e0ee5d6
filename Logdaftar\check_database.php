<?php
/**
 * Script untuk memeriksa dan memperbaiki struktur pangkalan data
 * Sistem Tempahan Bilik Mesyuarat
 */

require_once '../config/sistem_config.php';

echo "<!DOCTYPE html>
<html lang='ms'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Pemeriksaan Pangkalan Data</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>";

echo "<h1>Pemeriksaan Pangkalan Data - Sistem Tempahan Bilik Mesyuarat</h1>";

try {
    // Test database connection
    echo "<h2>1. Sambungan Pangkalan Data</h2>";
    $pdo = $db->getConnection();
    echo "<p class='success'>✓ Sambungan pangkalan data berjaya</p>";
    
    // Check if required tables exist
    echo "<h2>2. Pemeriksaan Jadual</h2>";
    $required_tables = ['pengguna', 'tbahagian', 'tunit', 'tjawatan', 'tgred'];
    $existing_tables = [];
    
    foreach ($required_tables as $table) {
        try {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() > 0) {
                echo "<p class='success'>✓ Jadual '$table' wujud</p>";
                $existing_tables[] = $table;
            } else {
                echo "<p class='error'>✗ Jadual '$table' tidak wujud</p>";
            }
        } catch (Exception $e) {
            echo "<p class='error'>✗ Ralat memeriksa jadual '$table': " . $e->getMessage() . "</p>";
        }
    }
    
    // Check pengguna table structure
    if (in_array('pengguna', $existing_tables)) {
        echo "<h2>3. Struktur Jadual Pengguna</h2>";
        try {
            $stmt = $pdo->query("DESCRIBE pengguna");
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>Medan</th><th>Jenis</th><th>Null</th><th>Kunci</th><th>Lalai</th></tr>";
            foreach ($columns as $column) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
                echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
                echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
                echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
                echo "<td>" . htmlspecialchars($column['Default']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            // Check for required columns
            $required_columns = ['id', 'nokp', 'kata_laluan', 'nama_penuh', 'emel', 'bahagian_id', 'unit_id', 'jawatan_id', 'gred_id'];
            $existing_columns = array_column($columns, 'Field');
            
            echo "<h3>Pemeriksaan Medan Diperlukan:</h3>";
            foreach ($required_columns as $col) {
                if (in_array($col, $existing_columns)) {
                    echo "<p class='success'>✓ Medan '$col' wujud</p>";
                } else {
                    echo "<p class='error'>✗ Medan '$col' tidak wujud</p>";
                }
            }
            
        } catch (Exception $e) {
            echo "<p class='error'>✗ Ralat memeriksa struktur jadual pengguna: " . $e->getMessage() . "</p>";
        }
    }
    
    // Check data in lookup tables
    echo "<h2>4. Pemeriksaan Data</h2>";
    $lookup_tables = ['tbahagian', 'tunit', 'tjawatan', 'tgred'];
    
    foreach ($lookup_tables as $table) {
        if (in_array($table, $existing_tables)) {
            try {
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
                $result = $stmt->fetch();
                $count = $result['count'];
                
                if ($count > 0) {
                    echo "<p class='success'>✓ Jadual '$table' mempunyai $count rekod</p>";
                    
                    // Show sample data
                    $stmt = $pdo->query("SELECT * FROM $table LIMIT 5");
                    $sample_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    
                    if (!empty($sample_data)) {
                        echo "<details><summary>Contoh data $table</summary>";
                        echo "<pre>" . print_r($sample_data, true) . "</pre>";
                        echo "</details>";
                    }
                } else {
                    echo "<p class='warning'>⚠ Jadual '$table' kosong</p>";
                }
            } catch (Exception $e) {
                echo "<p class='error'>✗ Ralat memeriksa data jadual '$table': " . $e->getMessage() . "</p>";
            }
        }
    }
    
    // Test insert operation
    echo "<h2>5. Ujian Operasi Pangkalan Data</h2>";
    try {
        // Test if we can perform a simple select
        $stmt = $pdo->query("SELECT 1 as test");
        $result = $stmt->fetch();
        if ($result['test'] == 1) {
            echo "<p class='success'>✓ Operasi SELECT berjaya</p>";
        }
        
        // Test if we can check for existing user (without inserting)
        if (in_array('pengguna', $existing_tables)) {
            $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM pengguna WHERE nokp = ? OR emel = ?");
            $stmt->execute(['999999999999', '<EMAIL>']);
            $result = $stmt->fetch();
            echo "<p class='success'>✓ Operasi pemeriksaan pengguna berjaya</p>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>✗ Ralat ujian operasi: " . $e->getMessage() . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>✗ Ralat utama: " . $e->getMessage() . "</p>";
}

echo "<h2>6. Cadangan Tindakan</h2>";
echo "<p>Jika terdapat jadual yang tidak wujud atau data yang kosong, sila:</p>";
echo "<ol>";
echo "<li>Import fail SQL dari folder 'database/sistem_tempahan_bilik.sql'</li>";
echo "<li>Atau jalankan skrip setup dari folder 'BuatTempahan/setup_dengan_penyelaras.php'</li>";
echo "<li>Pastikan semua jadual mempunyai data yang diperlukan</li>";
echo "</ol>";

echo "<p><a href='daftar.php'>← Kembali ke halaman pendaftaran</a></p>";

echo "</body></html>";
?>
