 <?php
/**
 * Halaman Hapus Bilik Mesyuarat
 * Sistem Tempahan Bilik Mesyuarat
 */

require_once '../config/sistem_config.php';

// Semak login dan peranan admin
perluLogin();
if (!isset($_SESSION['profile_id']) || $_SESSION['profile_id'] != 2) {
    header('Location: senarai_bilik.php?error=access_denied');
    exit;
}

$bilik_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$bilik_id) {
    header('Location: senarai_bilik.php?error=invalid_id');
    exit;
}

try {
    // Dapatkan maklumat bilik
    $bilik = $db->fetch("SELECT * FROM tbilik_mesyuarat WHERE id = ?", [$bilik_id]);
    
    if (!$bilik) {
        header('Location: senarai_bilik.php?error=bilik_not_found');
        exit;
    }
    
    // Semak jika ada tempahan aktif untuk bilik ini
    $tempahan_aktif = $db->fetch("
        SELECT COUNT(*) as jumlah 
        FROM ttempahan 
        WHERE idbilik_mesyuarat = ? AND kelulusan IN ('LULUS', 'MENUNGGU') 
        AND tarikh_tamat >= NOW()
    ", [$bilik_id]);
    
    if ($tempahan_aktif && $tempahan_aktif['jumlah'] > 0) {
        // Jika ada tempahan aktif, redirect dengan mesej ralat
        header('Location: senarai_bilik.php?error=has_bookings&count=' . $tempahan_aktif['jumlah']);
        exit;
    }
    
    // Hapus kemudahan bilik terlebih dahulu
    $db->query("DELETE FROM tbilik_kemudahan WHERE idbilik_mesyuarat = ?", [$bilik_id]);
    
    // Hapus bilik
    $result = $db->query("DELETE FROM tbilik_mesyuarat WHERE id = ?", [$bilik_id]);
    
    if ($result) {
        // Log aktiviti
        logAktiviti($_SESSION['pengguna_id'], 'Hapus Bilik', "Bilik '{$bilik['nama_bilik_mesyuarat']}' dihapus");
        
        // Redirect dengan mesej kejayaan
        header('Location: senarai_bilik.php?success=bilik_deleted&name=' . urlencode($bilik['nama_bilik_mesyuarat']));
        exit;
    } else {
        // Redirect dengan mesej ralat
        header('Location: senarai_bilik.php?error=delete_failed');
        exit;
    }
    
} catch (Exception $e) {
    // Log ralat
    error_log("Error deleting bilik: " . $e->getMessage());
    
    // Redirect dengan mesej ralat
    header('Location: senarai_bilik.php?error=database_error');
    exit;
}
?>

