-- Active: 1754285463565@@127.0.0.1@3306@sistem_tempahan_bilik
-- =====================================================
-- INSERT DATA UNTUK SISTEM TEMPAHAN BILIK MESYUARAT
-- =====================================================

-- Disable foreign key checks
SET FOREIGN_KEY_CHECKS = 0;

-- Kosongkan jadual terlebih dahulu
TRUNCATE TABLE `tgred`;
TRUNCATE TABLE `tjawatan`;
TRUNCATE TABLE `tbahagian`;

-- Memasukkan data gred
INSERT INTO `tgred`(`id`,`gred`) VALUES 
(1,'B22'),
(2,'C27'),
(3,'C32'),
(4,'C41'),
(5,'C44'),
(6,'C48'),
(7,'C52'),
(8,'F29'),
(9,'F32'),
(10,'F41'),
(11,'F44'),
(12,'FT17'),
(13,'J17'),
(14,'J29'),
(15,'J41'),
(16,'J44'),
(17,'J48'),
(18,'KP17'),
(19,'M41'),
(20,'M44'),
(21,'M48'),
(22,'M52'),
(23,'N1'),
(24,'N17'),
(25,'N22'),
(26,'N26'),
(27,'N27'),
(28,'N28'),
(29,'N32'),
(30,'N36'),
(31,'N4'),
(32,'N41'),
(33,'R1'),
(34,'R3'),
(35,'R4'),
(36,'R6'),
(37,'S41'),
(38,'S44'),
(39,'S48'),
(40,'U17'),
(41,'U29'),
(42,'U32'),
(43,'U36'),
(44,'U38'),
(45,'U41'),
(46,'U42'),
(47,'U44'),
(48,'U48'),
(49,'U52'),
(50,'U54'),
(51,'UD44'),
(52,'UD48'),
(53,'UD51'),
(54,'UD52'),
(55,'UD54'),
(56,'W17'),
(57,'W22'),
(58,'W27'),
(59,'W36'),
(60,'W44'),
(62,'M48'),
(63,'M54'),
(64,'H11'),
(65,'N11'),
(66,'R11');

-- Memasukkan data jawatan
INSERT INTO `tjawatan`(`id`,`jawatan`) VALUES 
(2,'JURUAUDIO VISUAL'),
(3,'JURURAWAT'),
(4,'JURURAWAT PERGIGIAN'),
(5,'JURUTEKNIK'),
(6,'JURUTEKNIK KOMPUTER'),
(7,'JURUTEKNOLOGI MAKMAL PERUBATAN'),
(8,'JURUTERA (AWAM)'),
(9,'JURUTERA (ELEKTRIK)'),
(10,'JURUTERA (KESIHATAN UMUM)'),
(11,'JURUTERA (MEKANIKAL)'),
(12,'PEGAWAI FARMASI'),
(14,'PEGAWAI KAUNSELOR'),
(15,'PEGAWAI KESIHATAN PERSEKITARAN'),
(16,'PEGAWAI KHIDMAT PELANGGAN'),
(18,'PEGAWAI PERGIGIAN'),
(19,'PEGAWAI PERGIGIAN'),
(20,'PEGAWAI PERUBATAN'),
(22,'PEGAWAI SAINS'),
(23,'PEGAWAI SAINS (KIMIA HAYAT)'),
(24,'PEGAWAI SAINS (PEGAWAI ZAT MAKANAN)'),
(26,'PEGAWAI TADBIR DAN DIPLOMATIK'),
(27,'PEGAWAI TEKNOLOGI MAKANAN'),
(28,'PEGAWAI TEKNOLOGI MAKLUMAT'),
(29,'PEKERJA AWAM'),
(30,'PEKERJA RENDAH AWAM'),
(31,'PEMANDU KENDERAAN'),
(32,'PEMBANTU AM PEJABAT'),
(33,'PEMBANTU KESELAMATAN'),
(34,'PEMBANTU KESIHATAN AWAM'),
(35,'PEMBANTU TADBIR (KESETIAUSAHAAN)'),
(36,'PEMBANTU TADBIR (KEWANGAN)'),
(37,'PEMBANTU TADBIR (P/O)'),
(39,'PEMBANTU TEKNIK'),
(40,'PEN. PEG. TEKNOLOGI MAKANAN'),
(41,'PENOLONG AKAUNTAN'),
(42,'PENOLONG JURUTERA'),
(43,'PENOLONG PEGAWAI KESIHATAN PERSEKITARAN'),
(44,'PENOLONG PEGAWAI PERUBATAN'),
(45,'PENOLONG PEGAWAI SAINS'),
(46,'PENOLONG PEGAWAI TADBIR'),
(47,'PEN. PEGAWAI TADBIR (REKOD PERUBATAN)'),
(48,'PEN. PEGAWAI TEKNOLOGI MAKLUMAT'),
(49,'PEREKA'),
(50,'SETIAUSAHA PEJABAT'),
(52,'TIMB. PENGARAH KESIHATAN NEGERI (PENGURUSAN)'),
(53,'PENGARAH KESIHATAN NEGERI'),
(54,'PENGARAH HOSPITAL');

-- Memasukkan data bahagian
INSERT INTO `tbahagian`(`id`,`bahagian`,`idptj`) VALUES 
(1,'Kesihatan Awam',NULL),
(2,'Perubatan',NULL),
(3,'Pengurusan',NULL),
(4,'Pergigian',NULL),
(5,'Farmasi',NULL),
(6,'Keselamatan & Kualiti Makanan',NULL);

-- Enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;
