<?php
/**
 * Script untuk menginisialisasi pangkalan data
 * Sistem Tempahan Bilik Mesyuarat
 */

require_once '../config/sistem_config.php';

echo "<!DOCTYPE html>
<html lang='ms'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Inisialisasi Pangkalan Data</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
    </style>
</head>
<body>";

echo "<h1>Inisialisasi Pangkalan Data - Sistem Tempahan Bilik Mesyuarat</h1>";

try {
    $pdo = $db->getConnection();
    echo "<p class='success'>✓ Sambungan pangkalan data berjaya</p>";
    
    // Create tables if they don't exist
    echo "<h2>Mencipta Jadual...</h2>";
    
    // Table: tbahagian
    $sql_bahagian = "CREATE TABLE IF NOT EXISTS `tbahagian` (
        `id` int(6) NOT NULL,
        `bahagian` varchar(100) DEFAULT NULL,
        `idptj` int(6) DEFAULT NULL,
        PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
    
    $pdo->exec($sql_bahagian);
    echo "<p class='success'>✓ Jadual tbahagian</p>";
    
    // Table: tunit
    $sql_unit = "CREATE TABLE IF NOT EXISTS `tunit` (
        `id` int(6) NOT NULL,
        `unit` varchar(100) DEFAULT NULL,
        `idbahagian` int(6) DEFAULT NULL,
        PRIMARY KEY (`id`),
        KEY `fk_unit_bahagian` (`idbahagian`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
    
    $pdo->exec($sql_unit);
    echo "<p class='success'>✓ Jadual tunit</p>";
    
    // Table: tjawatan
    $sql_jawatan = "CREATE TABLE IF NOT EXISTS `tjawatan` (
        `id` int(100) NOT NULL,
        `jawatan` varchar(100) DEFAULT NULL,
        PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
    
    $pdo->exec($sql_jawatan);
    echo "<p class='success'>✓ Jadual tjawatan</p>";
    
    // Table: tgred
    $sql_gred = "CREATE TABLE IF NOT EXISTS `tgred` (
        `id` int(11) NOT NULL,
        `gred` varchar(10) DEFAULT NULL,
        PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
    
    $pdo->exec($sql_gred);
    echo "<p class='success'>✓ Jadual tgred</p>";
    
    // Table: pengguna - check if table exists first
    $table_exists = $pdo->query("SHOW TABLES LIKE 'pengguna'")->rowCount() > 0;

    if (!$table_exists) {
        $sql_pengguna = "CREATE TABLE pengguna (
            id INT AUTO_INCREMENT PRIMARY KEY,
            nokp VARCHAR(12) UNIQUE NOT NULL,
            kata_laluan VARCHAR(255) NOT NULL,
            nama_penuh VARCHAR(100) NOT NULL,
            emel VARCHAR(100) UNIQUE NOT NULL,
            no_telefon VARCHAR(20),
            bahagian_id INT,
            unit_id INT,
            jawatan_id INT,
            gred_id INT,
            peranan ENUM('pentadbir', 'pengguna', 'penyelaras') DEFAULT 'pengguna',
            status ENUM('aktif', 'tidak_aktif') DEFAULT 'aktif',
            tarikh_daftar TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            tarikh_kemaskini TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        $pdo->exec($sql_pengguna);
        echo "<p class='success'>✓ Jadual pengguna dicipta</p>";
    } else {
        // Check if peranan column exists, if not add it
        $columns = $pdo->query("SHOW COLUMNS FROM pengguna")->fetchAll(PDO::FETCH_COLUMN);

        if (!in_array('peranan', $columns) && !in_array('profile_id', $columns)) {
            try {
                $pdo->exec("ALTER TABLE pengguna ADD COLUMN peranan ENUM('pentadbir', 'pengguna', 'penyelaras') DEFAULT 'pengguna'");
                echo "<p class='success'>✓ Kolom peranan ditambah ke jadual pengguna</p>";
            } catch (Exception $e) {
                echo "<p class='warning'>⚠ Tidak dapat menambah kolom peranan: " . $e->getMessage() . "</p>";
            }
        }
        echo "<p class='info'>ℹ Jadual pengguna sudah wujud</p>";
    }
    
    // Insert sample data if tables are empty
    echo "<h2>Memasukkan Data Asas...</h2>";
    
    // Check and insert bahagian data
    $count = $pdo->query("SELECT COUNT(*) FROM tbahagian")->fetchColumn();
    if ($count == 0) {
        $pdo->exec("INSERT INTO `tbahagian`(`id`,`bahagian`,`idptj`) VALUES 
            (1,'Pentadbiran & Kesihatan Awam',NULL),
            (2,'Perubatan',NULL),
            (3,'Pengurusan',NULL),
            (4,'Pergigian',NULL),
            (5,'Farmasi',NULL),
            (6,'Teknologi Maklumat dan Komunikasi',NULL),
            (7,'Penyelidikan dan Pembangunan',NULL),
            (8,'Latihan dan Pembangunan',NULL)");
        echo "<p class='success'>✓ Data bahagian dimasukkan</p>";
    } else {
        echo "<p class='info'>ℹ Data bahagian sudah wujud ($count rekod)</p>";
    }
    
    // Check and insert unit data
    $count = $pdo->query("SELECT COUNT(*) FROM tunit")->fetchColumn();
    if ($count == 0) {
        $pdo->exec("INSERT INTO `tunit`(`id`,`unit`,`idbahagian`) VALUES 
            (1,'Unit Pentadbiran',1),
            (2,'Unit Kesihatan Awam',1),
            (3,'Unit Perubatan Am',2),
            (4,'Unit Pakar',2),
            (5,'Unit Pengurusan Korporat',3),
            (6,'Unit Kewangan',3),
            (7,'Unit Pergigian Am',4),
            (8,'Unit Pergigian Pakar',4),
            (9,'Unit Farmasi Klinikal',5),
            (10,'Unit Farmasi Komuniti',5),
            (11,'Unit Sistem Maklumat',6),
            (12,'Unit Sokongan Teknikal',6),
            (13,'Unit Penyelidikan',7),
            (14,'Unit Pembangunan',7),
            (15,'Unit Latihan',8),
            (16,'Unit Pembangunan Sumber Manusia',8)");
        echo "<p class='success'>✓ Data unit dimasukkan</p>";
    } else {
        echo "<p class='info'>ℹ Data unit sudah wujud ($count rekod)</p>";
    }
    
    // Check and insert jawatan data
    $count = $pdo->query("SELECT COUNT(*) FROM tjawatan")->fetchColumn();
    if ($count == 0) {
        $pdo->exec("INSERT INTO `tjawatan`(`id`,`jawatan`) VALUES 
            (1,'Pegawai Perubatan'),
            (2,'Pegawai Farmasi'),
            (3,'Pegawai Pergigian'),
            (4,'Pegawai Kesihatan Awam'),
            (5,'Pegawai Pentadbiran'),
            (6,'Pegawai Teknologi Maklumat'),
            (7,'Jururawat'),
            (8,'Pembantu Perubatan'),
            (9,'Pembantu Farmasi'),
            (10,'Pembantu Pergigian'),
            (11,'Penolong Pegawai Kesihatan Awam'),
            (12,'Penolong Pegawai Pentadbiran'),
            (13,'Juruteknik'),
            (14,'Pemandu'),
            (15,'Pekerja Am')");
        echo "<p class='success'>✓ Data jawatan dimasukkan</p>";
    } else {
        echo "<p class='info'>ℹ Data jawatan sudah wujud ($count rekod)</p>";
    }
    
    // Check and insert gred data
    $count = $pdo->query("SELECT COUNT(*) FROM tgred")->fetchColumn();
    if ($count == 0) {
        $pdo->exec("INSERT INTO `tgred`(`id`,`gred`) VALUES 
            (1,'UD54'),
            (2,'UD52'),
            (3,'UD48'),
            (4,'UD44'),
            (5,'UD41'),
            (6,'U54'),
            (7,'U52'),
            (8,'U48'),
            (9,'U44'),
            (10,'U41'),
            (11,'U32'),
            (12,'U29'),
            (13,'U22'),
            (14,'U19'),
            (15,'U17'),
            (16,'U14'),
            (17,'U11'),
            (18,'N32'),
            (19,'N29'),
            (20,'N22'),
            (21,'N19'),
            (22,'N17'),
            (23,'N14'),
            (24,'N11'),
            (25,'H54'),
            (26,'H52'),
            (27,'H48'),
            (28,'H44'),
            (29,'H41'),
            (30,'H32'),
            (31,'H29'),
            (32,'H22'),
            (33,'H19'),
            (34,'H17'),
            (35,'H14'),
            (36,'H11')");
        echo "<p class='success'>✓ Data gred dimasukkan</p>";
    } else {
        echo "<p class='info'>ℹ Data gred sudah wujud ($count rekod)</p>";
    }
    
    echo "<h2>Selesai!</h2>";
    echo "<p class='success'>✓ Pangkalan data telah bersedia untuk digunakan</p>";
    echo "<p><a href='daftar.php'>← Kembali ke halaman pendaftaran</a></p>";
    
} catch (Exception $e) {
    echo "<p class='error'>✗ Ralat: " . $e->getMessage() . "</p>";
}

echo "</body></html>";
?>
