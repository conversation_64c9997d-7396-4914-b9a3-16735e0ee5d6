<?php
/**
 * Temp<PERSON> Saya
 * Sistem Tempahan Bilik Mesyuarat
 */

require_once '../config/sistem_config.php';

// Muatkan fungsi peranan pengguna
require_once '../includes/user_roles.php';

// Tentukan tajuk halaman berdasarkan peranan pengguna
$is_admin = isAdmin();
$tajuk_halaman = $is_admin ? 'Semua Tempahan' : 'Tempahan Saya';

// Semak login
perluLogin();

// Parameter untuk penapis dan pagination
$status_filter = $_GET['status'] ?? '';
$bulan_filter = $_GET['bulan'] ?? date('Y-m');
$user_filter = $_GET['user_filter'] ?? ''; // Filter pengguna untuk admin
$show_all_dates = $_GET['show_all_dates'] ?? ''; // New filter for all dates
$page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$limit = 10;
$offset = ($page - 1) * $limit;

// Bina query berdasarkan peranan pengguna
$where_conditions = [];
$params = [];

// Jika bukan admin, hanya tunjukkan tempahan sendiri
if (!$is_admin) {
    $where_conditions[] = "t.idpemohon = ?";
    $params[] = $_SESSION['pengguna_id'];
}

if (!empty($status_filter)) {
    $where_conditions[] = "t.kelulusan = ?";
    $params[] = strtoupper($status_filter);
}

// Filter tarikh - jika show_all_dates tidak diset, gunakan filter bulan
if (empty($show_all_dates)) {
    if (!empty($bulan_filter)) {
        $where_conditions[] = "DATE_FORMAT(t.tarikh_mula, '%Y-%m') = ?";
        $params[] = $bulan_filter;
    }
} else {
    // Jika show_all_dates diset, tidak tambah filter tarikh
    // Ini akan menunjukkan semua tempahan dari semua tarikh
}

// Filter pengguna untuk admin
if ($is_admin && !empty($user_filter)) {
    $where_conditions[] = "t.idpemohon = ?";
    $params[] = $user_filter;
}

// Bina WHERE clause
$where_clause = !empty($where_conditions) ? implode(' AND ', $where_conditions) : '1=1';

try {
    // Dapatkan senarai pengguna untuk filter admin
    $senarai_pengguna = [];
    if ($is_admin) {
        $sql_users = "SELECT id, nama_penuh FROM pengguna ORDER BY nama_penuh";
        $senarai_pengguna = $db->fetchAll($sql_users);
    }

    // Dapatkan jumlah keseluruhan tempahan untuk pagination
    $sql_count = "SELECT COUNT(*) as total
                  FROM ttempahan t
                  WHERE $where_clause AND (t.batal_tempahan IS NULL OR t.batal_tempahan != 'BATAL')";
    $total_result = $db->fetch($sql_count, $params);
    $total_tempahan = $total_result['total'];
    $total_pages = ceil($total_tempahan / $limit);

    // Dapatkan senarai tempahan dengan pagination
    $sql = "SELECT t.*, p.nama_penuh as nama_penyelulusan, pemohon.nama_penuh as nama_pemohon,
                   b.nama_bilik_mesyuarat
            FROM ttempahan t
            LEFT JOIN pengguna p ON t.idpegawai_pelulus = p.id
            LEFT JOIN pengguna pemohon ON t.idpemohon = pemohon.id
            LEFT JOIN tbilik_mesyuarat b ON t.idbilik_mesyuarat = b.id
            WHERE $where_clause AND (t.batal_tempahan IS NULL OR t.batal_tempahan != 'BATAL')
            ORDER BY t.tarikh_mula DESC
            LIMIT $limit OFFSET $offset";

    $senarai_tempahan = $db->fetchAll($sql, $params);


    
    // Dapatkan statistik tempahan berdasarkan peranan
    if ($is_admin) {
        // Admin: statistik semua tempahan
        $sql_stats = "SELECT
                        COUNT(*) as jumlah_keseluruhan,
                        SUM(CASE WHEN kelulusan = 'MENUNGGU' THEN 1 ELSE 0 END) as menunggu,
                        SUM(CASE WHEN kelulusan = 'LULUS' THEN 1 ELSE 0 END) as diluluskan,
                        SUM(CASE WHEN kelulusan = 'TOLAK' THEN 1 ELSE 0 END) as ditolak,
                        SUM(CASE WHEN batal_tempahan = 'BATAL' THEN 1 ELSE 0 END) as dibatalkan,
                        SUM(CASE WHEN kelulusan = 'SELESAI' THEN 1 ELSE 0 END) as selesai
                      FROM ttempahan";
        $statistik = $db->fetch($sql_stats, []);
    } else {
        // Pengguna biasa: statistik tempahan sendiri sahaja
        $sql_stats = "SELECT
                        COUNT(*) as jumlah_keseluruhan,
                        SUM(CASE WHEN kelulusan = 'MENUNGGU' THEN 1 ELSE 0 END) as menunggu,
                        SUM(CASE WHEN kelulusan = 'LULUS' THEN 1 ELSE 0 END) as diluluskan,
                        SUM(CASE WHEN kelulusan = 'TOLAK' THEN 1 ELSE 0 END) as ditolak,
                        SUM(CASE WHEN batal_tempahan = 'BATAL' THEN 1 ELSE 0 END) as dibatalkan,
                        SUM(CASE WHEN kelulusan = 'SELESAI' THEN 1 ELSE 0 END) as selesai
                      FROM ttempahan
                      WHERE idpemohon = ?";
        $statistik = $db->fetch($sql_stats, [$_SESSION['pengguna_id']]);
    }
    
} catch (Exception $e) {
    $senarai_tempahan = [];
    $statistik = [
        'jumlah_keseluruhan' => 0, 'menunggu' => 0, 'diluluskan' => 0,
        'ditolak' => 0, 'dibatalkan' => 0, 'selesai' => 0
    ];
    setMesejFlash('ralat', 'Ralat memuatkan data tempahan: ' . $e->getMessage());
}

// Proses tindakan tempahan (remove)
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action_type'])) {
    $action_type = $_POST['action_type'];
    $tempahan_id = (int)$_POST['tempahan_id'];

    try {
        if ($action_type == 'remove') {
            // Admin boleh padam semua tempahan
            // Pengguna biasa hanya boleh padam tempahan sendiri
            if ($is_admin) {
                $sql_check = "SELECT * FROM ttempahan WHERE id = ? AND kelulusan IN ('MENUNGGU', 'LULUS')";
                $tempahan_check = $db->fetch($sql_check, [$tempahan_id]);
            } else {
                $sql_check = "SELECT * FROM ttempahan WHERE id = ? AND idpemohon = ? AND kelulusan IN ('MENUNGGU', 'LULUS')";
                $tempahan_check = $db->fetch($sql_check, [$tempahan_id, $_SESSION['pengguna_id']]);
            }

            if ($tempahan_check) {
                // Padam tempahan sepenuhnya
                $sql_delete = "DELETE FROM ttempahan WHERE id = ?";
                $result = $db->query($sql_delete, [$tempahan_id]);

                if ($result) {
                    $success_msg = $is_admin && $tempahan_check['idpemohon'] != $_SESSION['pengguna_id'] 
                        ? 'Tempahan "' . $tempahan_check['tajuk_mesyuarat'] . '" berjaya dipadam (Admin).'
                        : 'Tempahan "' . $tempahan_check['tajuk_mesyuarat'] . '" berjaya dipadam.';
                    setMesejFlash('kejayaan', $success_msg);
                } else {
                    setMesejFlash('ralat', 'Ralat semasa memadam tempahan.');
                }
            } else {
                setMesejFlash('ralat', 'Tempahan tidak dijumpai atau tidak boleh dipadam.');
            }
        }

        // Redirect untuk elakkan resubmission
        header('Location: ' . $_SERVER['PHP_SELF']);
        exit;

    } catch (Exception $e) {
        setMesejFlash('ralat', 'Ralat sistem: ' . $e->getMessage());
    }
}

require_once '../includes/header_sistem.php';
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0"><?= $tajuk_halaman ?></h1>
                <p class="text-muted mb-0">
                    <?= $is_admin ? 'Senarai semua tempahan bilik mesyuarat dalam sistem' : 'Senarai tempahan bilik mesyuarat anda' ?>
                </p>
            </div>
            <div>
                <a href="../BuatTempahan/tempah_bilik_sebenar.php" class="btn btn-primary">
                    <i class="bi bi-calendar-plus me-2"></i>Tempahan Baru
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Statistik Tempahan -->
<div class="row mb-4">
    <div class="col-md-2 mb-2">
        <div class="card text-center">
            <div class="card-body py-3">
                <h5 class="text-primary mb-1"><?= $statistik['jumlah_keseluruhan'] ?></h5>
                <small class="text-muted">Jumlah</small>
            </div>
        </div>
    </div>
    <div class="col-md-2 mb-2">
        <div class="card text-center">
            <div class="card-body py-3">
                <h5 class="text-warning mb-1"><?= $statistik['menunggu'] ?></h5>
                <small class="text-muted">Menunggu</small>
            </div>
        </div>
    </div>
    <div class="col-md-2 mb-2">
        <div class="card text-center">
            <div class="card-body py-3">
                <h5 class="text-success mb-1"><?= $statistik['diluluskan'] ?></h5>
                <small class="text-muted">Diluluskan</small>
            </div>
        </div>
    </div>
    <div class="col-md-2 mb-2">
        <div class="card text-center">
            <div class="card-body py-3">
                <h5 class="text-danger mb-1"><?= $statistik['ditolak'] ?></h5>
                <small class="text-muted">Ditolak</small>
            </div>
        </div>
    </div>
    <div class="col-md-2 mb-2">
        <div class="card text-center">
            <div class="card-body py-3">
                <h5 class="text-secondary mb-1"><?= $statistik['dibatalkan'] ?></h5>
                <small class="text-muted">Dibatalkan</small>
            </div>
        </div>
    </div>
    <div class="col-md-2 mb-2">
        <div class="card text-center">
            <div class="card-body py-3">
                <h5 class="text-info mb-1"><?= $statistik['selesai'] ?></h5>
                <small class="text-muted">Selesai</small>
            </div>
        </div>
    </div>
</div>

<!-- Filter Section -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">Status</label>
                <select name="status" class="form-select">
                    <option value="">Semua Status</option>
                    <option value="MENUNGGU" <?= $status_filter == 'MENUNGGU' ? 'selected' : '' ?>>Menunggu</option>
                    <option value="LULUS" <?= $status_filter == 'LULUS' ? 'selected' : '' ?>>Diluluskan</option>
                    <option value="TOLAK" <?= $status_filter == 'TOLAK' ? 'selected' : '' ?>>Ditolak</option>
                    <option value="SELESAI" <?= $status_filter == 'SELESAI' ? 'selected' : '' ?>>Selesai</option>
                </select>
            </div>
            
            <div class="col-md-3">
                <label class="form-label">Bulan</label>
                <input type="month" name="bulan" class="form-control" value="<?= $bulan_filter ?>" 
                       <?= !empty($show_all_dates) ? 'disabled' : '' ?>>
            </div>

            <?php if ($is_admin): ?>
            <div class="col-md-3">
                <label class="form-label">Pengguna</label>
                <input type="text" name="user_filter" class="form-control" 
                       placeholder="Nama pengguna" value="<?= htmlspecialchars($user_filter) ?>">
            </div>
            <?php endif; ?>

            <div class="col-md-3">
                <label class="form-label">Paparan Tarikh</label>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" name="show_all_dates" value="1" 
                           id="showAllDates" <?= !empty($show_all_dates) ? 'checked' : '' ?>>
                    <label class="form-check-label" for="showAllDates">
                        Tunjuk Semua Tarikh
                    </label>
                </div>
            </div>

            <div class="col-12">
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-funnel me-2"></i>Tapis
                </button>
                <a href="<?= $_SERVER['PHP_SELF'] ?>" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-clockwise me-2"></i>Reset
                </a>
            </div>
        </form>
    </div>
</div>



<!-- Senarai Tempahan -->
<div class="row">
    <div class="col-12">
        <?php if (empty($senarai_tempahan)): ?>
            <div class="text-center py-5">
                <i class="bi bi-calendar-x fs-1 text-muted mb-3"></i>
                <h4 class="text-muted">Tiada tempahan dijumpai</h4>
                <p class="text-muted">Anda belum membuat sebarang tempahan atau tiada tempahan yang sepadan dengan kriteria penapis.</p>
                <a href="../BuatTempahan/tempah_bilik.php" class="btn btn-primary">
                    <i class="bi bi-calendar-plus me-2"></i>Buat Tempahan Pertama
                </a>
            </div>
        <?php else: ?>
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">Senarai Tempahan</h6>
                    <small class="text-muted">
                        Halaman <?= $page ?> daripada <?= $total_pages ?>
                        (<?= $total_tempahan ?> tempahan keseluruhan)
                    </small>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Bil</th>
                                    <th>Tajuk Mesyuarat</th>
                                    <th>Pemohon</th>
                                    <th>Tarikh</th>
                                    <th>Masa</th>
                                    <th>Pengerusi</th>
                                    <th>Status</th>
                                    <th>Tindakan</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $bil = ($page - 1) * $limit + 1; ?>
                                <?php foreach ($senarai_tempahan as $tempahan): ?>
                                    <?php
                                    $badge_class = '';
                                    $status_text = '';
                                    $status = $tempahan['batal_tempahan'] == 'BATAL' ? 'BATAL' : $tempahan['kelulusan'];
                                    switch ($status) {
                                        case 'MENUNGGU':
                                            $badge_class = 'bg-warning text-dark';
                                            $status_text = 'Menunggu';
                                            break;
                                        case 'LULUS':
                                            $badge_class = 'bg-success';
                                            $status_text = 'Diluluskan';
                                            break;
                                        case 'TOLAK':
                                            $badge_class = 'bg-danger';
                                            $status_text = 'Ditolak';
                                            break;
                                        case 'BATAL':
                                            $badge_class = 'bg-secondary';
                                            $status_text = 'Dibatalkan';
                                            break;
                                        case 'SELESAI':
                                            $badge_class = 'bg-info';
                                            $status_text = 'Selesai';
                                            break;
                                        default:
                                            $badge_class = 'bg-light text-dark';
                                            $status_text = ucfirst($status);
                                    }
                                    ?>
                                    <tr>
                                        <td class="text-center">
                                            <?= $bil ?>
                                        </td>
                                        <td>
                                            <strong><?= htmlspecialchars($tempahan['tajuk_mesyuarat']) ?></strong>
                                            <?php if (!empty($tempahan['ulasan'])): ?>
                                                <br><small class="text-muted"><i class="bi bi-chat-text me-1"></i><?= htmlspecialchars($tempahan['ulasan']) ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <i class="bi bi-person me-1"></i><?= htmlspecialchars($tempahan['nama_pemohon'] ?? 'Tidak diketahui') ?>
                                        </td>
                                        <td>
                                            <i class="bi bi-calendar me-1"></i><?= date('d/m/Y', strtotime($tempahan['tarikh_mula'])) ?>
                                        </td>
                                        <td>
                                            <i class="bi bi-clock me-1"></i><?= date('H:i', strtotime($tempahan['tarikh_mula'])) ?> - <?= date('H:i', strtotime($tempahan['tarikh_tamat'])) ?>
                                        </td>
                                        <td>
                                            <?= htmlspecialchars($tempahan['pengerusi']) ?>
                                        </td>
                                        <td class="text-center">
                                            <span class="badge <?= $badge_class ?>"><?= $status_text ?></span>
                                        </td>
                                        <td class="text-center">
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-info btn-sm"
                                                        data-bs-toggle="modal"
                                                        data-bs-target="#modalTempahan<?= $tempahan['id'] ?>"
                                                        title="Lihat Detail">
                                                    <i class="bi bi-eye"></i> View
                                                </button>

                                                <?php
                                                // Admin boleh modify semua tempahan
                                                // Pengguna biasa hanya boleh modify tempahan sendiri
                                                $can_modify = $is_admin || $tempahan['idpemohon'] == $_SESSION['pengguna_id'];
                                                ?>

                                                <?php if ($can_modify && in_array($status, ['MENUNGGU'])): ?>
                                                    <button class="btn btn-outline-warning btn-sm"
                                                            onclick="editTempahan(<?= $tempahan['id'] ?>)"
                                                            title="Edit Tempahan">
                                                        <i class="bi bi-pencil"></i> Edit
                                                    </button>
                                                <?php endif; ?>

                                                <?php if ($can_modify && in_array($status, ['MENUNGGU', 'LULUS'])): ?>
                                                    <button class="btn btn-outline-danger btn-sm"
                                                            onclick="removeTempahan(<?= $tempahan['id'] ?>, '<?= htmlspecialchars($tempahan['tajuk_mesyuarat']) ?>')"
                                                            title="Padam Tempahan">
                                                        <i class="bi bi-trash"></i> Remove
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php $bil++; ?>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <?php if ($total_pages > 1): ?>
                        <div class="d-flex justify-content-between align-items-center mt-4">
                            <div>
                                <small class="text-muted">
                                    Menunjukkan <?= (($page - 1) * $limit) + 1 ?> hingga <?= min($page * $limit, $total_tempahan) ?>
                                    daripada <?= $total_tempahan ?> tempahan
                                </small>
                            </div>
                            <nav aria-label="Pagination">
                                <ul class="pagination pagination-sm mb-0">
                                    <!-- Previous Button -->
                                    <?php if ($page > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $page - 1])) ?>">
                                                <i class="bi bi-chevron-left"></i> Sebelum
                                            </a>
                                        </li>
                                    <?php else: ?>
                                        <li class="page-item disabled">
                                            <span class="page-link"><i class="bi bi-chevron-left"></i> Sebelum</span>
                                        </li>
                                    <?php endif; ?>

                                    <!-- Page Numbers -->
                                    <?php
                                    $start_page = max(1, $page - 2);
                                    $end_page = min($total_pages, $page + 2);

                                    if ($start_page > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => 1])) ?>">1</a>
                                        </li>
                                        <?php if ($start_page > 2): ?>
                                            <li class="page-item disabled"><span class="page-link">...</span></li>
                                        <?php endif; ?>
                                    <?php endif; ?>

                                    <?php for ($i = $start_page; $i <= $end_page; $i++): ?>
                                        <li class="page-item <?= ($i == $page) ? 'active' : '' ?>">
                                            <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $i])) ?>"><?= $i ?></a>
                                        </li>
                                    <?php endfor; ?>

                                    <?php if ($end_page < $total_pages): ?>
                                        <?php if ($end_page < $total_pages - 1): ?>
                                            <li class="page-item disabled"><span class="page-link">...</span></li>
                                        <?php endif; ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $total_pages])) ?>"><?= $total_pages ?></a>
                                        </li>
                                    <?php endif; ?>

                                    <!-- Next Button -->
                                    <?php if ($page < $total_pages): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $page + 1])) ?>">
                                                Seterus <i class="bi bi-chevron-right"></i>
                                            </a>
                                        </li>
                                    <?php else: ?>
                                        <li class="page-item disabled">
                                            <span class="page-link">Seterus <i class="bi bi-chevron-right"></i></span>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Modals for each tempahan -->
            <?php foreach ($senarai_tempahan as $tempahan): ?>
                <?php
                $status = $tempahan['batal_tempahan'] == 'BATAL' ? 'BATAL' : $tempahan['kelulusan'];
                switch ($status) {
                    case 'MENUNGGU': $badge_class = 'bg-warning text-dark'; $status_text = 'Menunggu'; break;
                    case 'LULUS': $badge_class = 'bg-success'; $status_text = 'Diluluskan'; break;
                    case 'TOLAK': $badge_class = 'bg-danger'; $status_text = 'Ditolak'; break;
                    case 'BATAL': $badge_class = 'bg-secondary'; $status_text = 'Dibatalkan'; break;
                    case 'SELESAI': $badge_class = 'bg-info'; $status_text = 'Selesai'; break;
                    default: $badge_class = 'bg-light text-dark'; $status_text = ucfirst($status);
                }
                ?>

                <!-- Modal Detail Tempahan -->
                <div class="modal fade" id="modalTempahan<?= $tempahan['id'] ?>" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">Detail Tempahan - <?= htmlspecialchars($tempahan['tajuk_mesyuarat']) ?></h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="row">
                                    <div class="col-md-12">
                                        <h6>Maklumat Tempahan</h6>
                                        <table class="table table-sm">
                                            <tr>
                                                <td><strong>ID Tempahan:</strong></td>
                                                <td><?= $tempahan['id'] ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Pemohon:</strong></td>
                                                <td><?= htmlspecialchars($tempahan['nama_pemohon'] ?? 'Tidak diketahui') ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Tarikh Mohon:</strong></td>
                                                <td><?= date('d/m/Y H:i', strtotime($tempahan['tarikh_mohon'])) ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Nama Bilik Mesyuarat:</strong></td>
                                                <td><?= htmlspecialchars($tempahan['nama_bilik_mesyuarat'] ?? 'Tidak diketahui') ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Tajuk Mesyuarat:</strong></td>
                                                <td><?= htmlspecialchars($tempahan['tajuk_mesyuarat']) ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Tarikh Mula:</strong></td>
                                                <td><?= date('d/m/Y H:i', strtotime($tempahan['tarikh_mula'])) ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Tarikh Tamat:</strong></td>
                                                <td><?= date('d/m/Y H:i', strtotime($tempahan['tarikh_tamat'])) ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Status Kelulusan:</strong></td>
                                                <td><span class="badge <?= $badge_class ?>"><?= $status_text ?></span></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Nama Pegawai Pelulus:</strong></td>
                                                <td><?= htmlspecialchars($tempahan['nama_penyelulusan'] ?? 'Belum diluluskan') ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Tarikh Lulus:</strong></td>
                                                <td>
                                                    <?php if (!empty($tempahan['tarikh_lulus'])): ?>
                                                        <?= date('d/m/Y H:i', strtotime($tempahan['tarikh_lulus'])) ?>
                                                    <?php else: ?>
                                                        Belum diluluskan
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><strong>Bilangan Peserta:</strong></td>
                                                <td><?= $tempahan['bilangan_peserta'] ?> orang</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Pengerusi:</strong></td>
                                                <td><?= htmlspecialchars($tempahan['pengerusi']) ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Sesi:</strong></td>
                                                <td>
                                                    <?php
                                                    switch($tempahan['sesi']) {
                                                        case 1: echo 'Pagi (8:00 AM - 12:00 PM)'; break;
                                                        case 2: echo 'Petang (2:00 PM - 5:00 PM)'; break;
                                                        case 3: echo 'Sepanjang Hari (8:00 AM - 5:00 PM)'; break;
                                                        default: echo 'Tidak ditetapkan';
                                                    }
                                                    ?>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>

                                <?php if (!empty($tempahan['ulasan'])): ?>
                                <div class="mt-3">
                                    <h6>Ulasan/Catatan</h6>
                                    <div class="alert alert-info">
                                        <?= htmlspecialchars($tempahan['ulasan']) ?>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>

                                <?php
                                // Admin boleh modify semua tempahan
                                // Pengguna biasa hanya boleh modify tempahan sendiri
                                $can_modify_modal = $is_admin || $tempahan['idpemohon'] == $_SESSION['pengguna_id'];
                                ?>

                                <?php if ($can_modify_modal && in_array($status, ['MENUNGGU'])): ?>
                                    <button type="button" class="btn btn-warning"
                                            onclick="editTempahan(<?= $tempahan['id'] ?>)">
                                        <i class="bi bi-pencil me-2"></i>Edit Tempahan
                                    </button>
                                <?php endif; ?>

                                <?php if ($can_modify_modal && in_array($status, ['MENUNGGU', 'LULUS'])): ?>
                                    <button type="button" class="btn btn-danger"
                                            onclick="removeTempahan(<?= $tempahan['id'] ?>, '<?= htmlspecialchars($tempahan['tajuk_mesyuarat']) ?>')">
                                        <i class="bi bi-trash me-2"></i>Padam Tempahan
                                    </button>
                                <?php endif; ?>

                                <?php if ($is_admin && $tempahan['idpemohon'] != $_SESSION['pengguna_id']): ?>
                                    <small class="text-muted">Admin: Menguruskan tempahan <?= htmlspecialchars($tempahan['nama_pemohon']) ?></small>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
</div>

<!-- Form tersembunyi untuk tindakan -->
<form id="formAction" method="POST" style="display: none;">
    <input type="hidden" name="action_type" id="actionType">
    <input type="hidden" name="tempahan_id" id="actionTempahanId">
</form>

<?php
$custom_js = "
function removeTempahan(tempahanId, tajukMesyuarat) {
    if (confirm('Adakah anda pasti ingin memadam tempahan \"' + tajukMesyuarat + '\"?\\n\\nTindakan ini tidak boleh dibatalkan.')) {
        document.getElementById('actionType').value = 'remove';
        document.getElementById('actionTempahanId').value = tempahanId;
        document.getElementById('formAction').submit();
    }
}

function editTempahan(tempahanId) {
    // Redirect to edit page
    window.location.href = '../BuatTempahan/edit_tempahan.php?id=' + tempahanId;
}

function viewTempahan(tempahanId) {
    // Modal will handle this automatically
    console.log('Viewing tempahan ID: ' + tempahanId);
}
";

require_once '../includes/footer_sistem.php';
?>

<script>
// Toggle month filter when "Show All Dates" is checked
document.getElementById('showAllDates').addEventListener('change', function() {
    const monthInput = document.querySelector('input[name="bulan"]');
    if (this.checked) {
        monthInput.disabled = true;
        monthInput.value = '';
    } else {
        monthInput.disabled = false;
        monthInput.value = '<?= date('Y-m') ?>';
    }
});
</script>









