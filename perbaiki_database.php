<?php
/**
 * Script Perbaiki Database
 * Menambah kolum yang hilang dalam jadual pengguna
 */

require_once 'config/database.php';

echo "<h2>Script Perbaiki Database</h2>";

try {
    $db = new Database();
    
    // Fungsi untuk memeriksa jika kolum wujud
    function checkColumnExists($db, $table, $column) {
        try {
            $result = $db->fetchAll("SHOW COLUMNS FROM `$table` LIKE '$column'");
            return count($result) > 0;
        } catch (Exception $e) {
            return false;
        }
    }
    
    echo "<p>Memeriksa struktur database...</p>";
    
    $columns_to_check = [
        'bahagian_id' => 'INT',
        'unit_id' => 'INT', 
        'jawatan_id' => 'INT',
        'gred_id' => 'INT'
    ];
    
    $added_columns = [];
    $existing_columns = [];
    
    foreach ($columns_to_check as $column => $type) {
        if (!checkColumnExists($db, 'pengguna', $column)) {
            // Tambah kolum yang hilang
            $sql = "ALTER TABLE pengguna ADD COLUMN $column $type AFTER no_telefon";
            $db->query($sql);
            $added_columns[] = $column;
            echo "<p style='color: green;'>✓ Kolum '$column' berjaya ditambah</p>";
        } else {
            $existing_columns[] = $column;
            echo "<p style='color: blue;'>ℹ Kolum '$column' sudah wujud</p>";
        }
    }
    
    // Tambah foreign key constraints jika kolum baru ditambah
    if (!empty($added_columns)) {
        echo "<p>Menambah foreign key constraints...</p>";
        
        try {
            if (in_array('bahagian_id', $added_columns)) {
                $db->query("ALTER TABLE pengguna ADD CONSTRAINT fk_pengguna_bahagian 
                           FOREIGN KEY (bahagian_id) REFERENCES tbahagian(id) ON DELETE SET NULL");
                echo "<p style='color: green;'>✓ Foreign key untuk bahagian_id ditambah</p>";
            }
            
            if (in_array('unit_id', $added_columns)) {
                $db->query("ALTER TABLE pengguna ADD CONSTRAINT fk_pengguna_unit 
                           FOREIGN KEY (unit_id) REFERENCES tunit(id) ON DELETE SET NULL");
                echo "<p style='color: green;'>✓ Foreign key untuk unit_id ditambah</p>";
            }
            
            if (in_array('jawatan_id', $added_columns)) {
                $db->query("ALTER TABLE pengguna ADD CONSTRAINT fk_pengguna_jawatan 
                           FOREIGN KEY (jawatan_id) REFERENCES tjawatan(id) ON DELETE SET NULL");
                echo "<p style='color: green;'>✓ Foreign key untuk jawatan_id ditambah</p>";
            }
            
            if (in_array('gred_id', $added_columns)) {
                $db->query("ALTER TABLE pengguna ADD CONSTRAINT fk_pengguna_gred 
                           FOREIGN KEY (gred_id) REFERENCES tgred(id) ON DELETE SET NULL");
                echo "<p style='color: green;'>✓ Foreign key untuk gred_id ditambah</p>";
            }
        } catch (Exception $e) {
            echo "<p style='color: orange;'>⚠ Amaran: Tidak dapat menambah foreign key constraints: " . $e->getMessage() . "</p>";
            echo "<p>Ini mungkin kerana jadual rujukan tidak wujud. Kolum masih boleh digunakan.</p>";
        }
    }
    
    echo "<h3>Ringkasan:</h3>";
    if (!empty($added_columns)) {
        echo "<p style='color: green;'><strong>Kolum yang ditambah:</strong> " . implode(', ', $added_columns) . "</p>";
    }
    if (!empty($existing_columns)) {
        echo "<p style='color: blue;'><strong>Kolum yang sudah wujud:</strong> " . implode(', ', $existing_columns) . "</p>";
    }
    
    echo "<p style='color: green;'><strong>✓ Database berjaya diperbaiki!</strong></p>";
    echo "<p><a href='Menuutama/profil.php'>Kembali ke halaman profil</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Ralat: " . $e->getMessage() . "</p>";
    echo "<p>Pastikan:</p>";
    echo "<ul>";
    echo "<li>Database 'sistem_tempahan_bilik' wujud</li>";
    echo "<li>Jadual 'pengguna' wujud</li>";
    echo "<li>Sambungan database berfungsi</li>";
    echo "</ul>";
}
?>
