<?php
/**
 * Halaman Log Keluar
 * Sistem Tempahan Bilik Mesyuarat
 */

session_start();

// Log aktiviti sebelum destroy session (jika pengguna masih log masuk)
if (isset($_SESSION['pengguna_id'])) {
    require_once '../config/sistem_config.php';
    try {
        logAktiviti($_SESSION['pengguna_id'], 'Log Keluar', 'Pengguna log keluar dari sistem');
    } catch (Exception $e) {
        // Ignore error jika tidak dapat log aktiviti
    }
}

// Destroy session
session_destroy();

// Redirect ke halaman log masuk
header('Location: log_masuk.php');
exit;
?>
