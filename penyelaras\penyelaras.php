<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['pengguna_id'])) {
    header("Location: ../Logdaftar/log_masuk.php");
    exit();
}

// Database connection
require_once '../config/database.php';

$user_id = $_SESSION['pengguna_id'];

// Verify user is a coordinator
$is_coordinator = false;
if ($user_id) {
    $check_penyelaras = "SELECT peranan FROM pengguna WHERE id = ?";
    $stmt_check = $db->query($check_penyelaras, [$user_id]);
    $user_data = $stmt_check->fetch();

    if (isset($user_data['peranan']) && $user_data['peranan'] == 'penyelaras') {
        $is_coordinator = true;
    } else {
        header("Location: ../Menuutama/dashboard.php");
        exit();
    }
}

$activeMenu = isset($_GET['activeMenu']) ? $_GET['activeMenu'] : 'semua';
?>

<!DOCTYPE html>
<html lang="ms">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Penyelaras - Sistem Tempahan Bilik Mesyuarat</title>
    <link rel="stylesheet" href="home.css">
    <script defer src="home.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
</head>
<body>
    <!-- Tajuk Sistem -->
    <div class="tajukcontainer">
        <h1>SISTEM TEMPAHAN BILIK MESYUARAT</h1>
        <h3>JABATAN KESIHATAN NEGERI KEDAH</h3>
    </div>

    <!-- Navigasi Tab (converted to links) -->
    <div class="navigasi">
        <div class="wrap">
            <a href="tab1_kalendar.php" class="label">Kalendar</a>
            <a href="tab2_senarai.php" class="label">Senarai Tempahan</a>
            <a href="tab7_penyelaras.php" class="label active">Penyelaras</a>
            <a href="tab3_bilik.php" class="label">Bilik Mesyuarat</a>

            <?php
            // Periksa apakah pengguna adalah Penyelaras Bilik Mesyuarat atau Pentadbir
            if ($user_id) {
                $check_penyelaras = "SELECT peranan FROM pengguna WHERE id = ?";
                $stmt_check = $db->query($check_penyelaras, [$user_id]);
                $user_data = $stmt_check->fetch();

                if (isset($user_data['peranan']) && ($user_data['peranan'] == 'pentadbir' || $user_data['peranan'] == 'penyelaras')) {
            ?>
                <a href="tab4_kawalan.php" class="label">Kawalan</a>
            <?php
                }
            }
            ?>
            
            <a href="tab5_profil.php" class="label">Profil</a>
            <a href="../login/logoutconfirm.php" class="label">Log Keluar</a>
        </div>
    </div>

    <!-- Kandungan Tab 7: Penyelaras -->
    <div class="tab-container">
        <div class="container">
            <!-- Kawalan menu -->
            <div class="kawalan-menu">
                <div class="menu-item <?php echo $activeMenu == 'semua' ? 'active' : ''; ?>" onclick="showKawalanContent('semua', this)">Semua Tempahan</div>
                <div class="menu-item <?php echo $activeMenu == 'baru' ? 'active' : ''; ?>" onclick="showKawalanContent('baru', this)">Baru / Pending</div>
            </div>
        </div>
    </div>

    <!-- Remove duplicate HTML and PHP code -->
    <script>
        // Add any JavaScript functions here if needed
        function showKawalanContent(type, element) {
            window.location.href = '?activeMenu=' + type;
        }
    </script>
</body>
</html>

<?php
// Continue with the new structure
?>

<!DOCTYPE html>
<html lang="ms">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Penyelaras - Sistem Tempahan Bilik</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .nav-pills .nav-link {
            color: rgba(255,255,255,0.8);
            border-radius: 10px;
            margin: 5px 0;
        }
        .nav-pills .nav-link.active {
            background: rgba(255,255,255,0.2);
            color: white;
        }
        .nav-pills .nav-link:hover {
            background: rgba(255,255,255,0.1);
            color: white;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .table th {
            background: #f8f9fa;
            border: none;
            font-weight: 600;
        }
        .badge {
            font-size: 0.75em;
        }
        .btn-action {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar p-3">
                <div class="text-center mb-4">
                    <h4 class="text-white">
                        <i class="bi bi-people-fill"></i> Penyelaras
                    </h4>
                </div>

                <ul class="nav nav-pills flex-column">
                    <li class="nav-item">
                        <a class="nav-link <?php echo $activeMenu == 'semua' ? 'active' : ''; ?>"
                           href="?activeMenu=semua">
                            <i class="bi bi-list-ul"></i> Senarai Tempahan
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $activeMenu == 'senarai' ? 'active' : ''; ?>"
                           href="?activeMenu=senarai">
                            <i class="bi bi-people"></i> Senarai Penyelaras
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $activeMenu == 'mesyuarat' ? 'active' : ''; ?>"
                           href="?activeMenu=mesyuarat">
                            <i class="bi bi-calendar3"></i> Jadual Mesyuarat
                        </a>
                    </li>
                    <li class="nav-item mt-4">
                        <a class="nav-link" href="../Menuutama/dashboard.php">
                            <i class="bi bi-arrow-left"></i> Kembali ke Dashboard
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 p-4">
                <!-- Senarai Tempahan Content -->
                <?php if ($activeMenu == 'semua'): ?>
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="bi bi-list-ul"></i> Senarai Tempahan Penyelaras</h5>
                    </div>
                    <div class="card-body">
                        <?php
                        // Get user's numeric ID - user_id is already the numeric ID from session
                        $numeric_user_id = $user_id;

                        // Get bookings for rooms managed by this coordinator
                        $sql = "SELECT t.*, p.nama_penuh as nama_pemohon, bm.nama_bilik_mesyuarat,
                                DATE_FORMAT(t.tarikh_mula, '%d/%m/%Y %H:%i') as tarikh_mula_formatted,
                                DATE_FORMAT(t.tarikh_tamat, '%d/%m/%Y %H:%i') as tarikh_tamat_formatted,
                                DATE_FORMAT(t.tarikh_mohon, '%d/%m/%Y') as tarikh_mohon_formatted
                                FROM ttempahan t
                                JOIN pengguna p ON t.idpemohon = p.id
                                JOIN tbilik_mesyuarat bm ON t.idbilik_mesyuarat = bm.id
                                JOIN tbilik_penyelaras bp ON bm.id = bp.idbilik_mesyuarat
                                WHERE bp.idpenyelaras = ?
                                ORDER BY t.tarikh_mula DESC";

                        $stmt = $db->query($sql, [$numeric_user_id]);
                        $result = $stmt->fetchAll();
                        ?>
                        
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>No.</th>
                                        <th>ID</th>
                                        <th>Pemohon</th>
                                        <th>Tarikh Mohon</th>
                                        <th>Tajuk Mesyuarat</th>
                                        <th>Bilik</th>
                                        <th>Tarikh Mula</th>
                                        <th>Tarikh Tamat</th>
                                        <th>Status</th>
                                        <th>Tindakan</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    if (count($result) > 0) {
                                        $no = 1;
                                        foreach ($result as $row) {
                                            $status_class = match($row['kelulusan']) {
                                                'LULUS' => 'bg-success',
                                                'TOLAK' => 'bg-danger',
                                                'MENUNGGU' => 'bg-warning',
                                                default => 'bg-secondary'
                                            };
                                            ?>
                                            <tr>
                                                <td><?php echo $no++; ?></td>
                                                <td><?php echo $row['id']; ?></td>
                                                <td><?php echo htmlspecialchars($row['nama_pemohon']); ?></td>
                                                <td><?php echo $row['tarikh_mohon_formatted']; ?></td>
                                                <td><?php echo htmlspecialchars($row['tajuk_mesyuarat']); ?></td>
                                                <td><?php echo htmlspecialchars($row['nama_bilik_mesyuarat']); ?></td>
                                                <td><?php echo $row['tarikh_mula_formatted']; ?></td>
                                                <td><?php echo $row['tarikh_tamat_formatted']; ?></td>
                                                <td>
                                                    <span class="badge <?php echo $status_class; ?>">
                                                        <?php echo $row['kelulusan']; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php if ($row['kelulusan'] == 'MENUNGGU'): ?>
                                                        <button class="btn btn-success btn-action me-1"
                                                                onclick="luluskanTempahan(<?php echo $row['id']; ?>)">
                                                            <i class="bi bi-check-lg"></i>
                                                        </button>
                                                        <button class="btn btn-danger btn-action"
                                                                onclick="tolakTempahan(<?php echo $row['id']; ?>)">
                                                            <i class="bi bi-x-lg"></i>
                                                        </button>
                                                    <?php else: ?>
                                                        <span class="text-muted">-</span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                            <?php
                                        }
                                    } else {
                                        echo "<tr><td colspan='10' class='text-center'>Tiada tempahan dijumpai</td></tr>";
                                    }
                                    ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Senarai Penyelaras Content -->
                <?php if ($activeMenu == 'senarai'): ?>
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="bi bi-people"></i> Senarai Penyelaras Bilik Mesyuarat</h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" class="mb-4">
                            <input type="hidden" name="activeMenu" value="senarai">
                            <div class="row">
                                <div class="col-md-6">
                                    <select name="bilik_id" class="form-select" onchange="this.form.submit()">
                                        <option value="all">Semua Bilik Mesyuarat</option>
                                        <?php
                                        $bilik_id = $_GET['bilik_id'] ?? 'all';
                                        $sql_bilik = "SELECT id, nama_bilik_mesyuarat FROM tbilik_mesyuarat ORDER BY nama_bilik_mesyuarat";
                                        $result_bilik = $db->fetchAll($sql_bilik);

                                        foreach ($result_bilik as $row_bilik) {
                                            $selected = ($bilik_id == $row_bilik['id']) ? 'selected' : '';
                                            echo "<option value='" . $row_bilik['id'] . "' $selected>" .
                                                 htmlspecialchars($row_bilik['nama_bilik_mesyuarat']) . "</option>";
                                        }
                                        ?>
                                    </select>
                                </div>
                            </div>
                        </form>

                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>No.</th>
                                        <th>Nama Penyelaras</th>
                                        <th>Jawatan</th>
                                        <th>Gred</th>
                                        <th>Bahagian</th>
                                        <th>No. Telefon</th>
                                        <?php if ($bilik_id == 'all'): ?>
                                        <th>Bilik Mesyuarat</th>
                                        <?php endif; ?>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    if ($bilik_id == 'all') {
                                        $sql = "SELECT p.id, p.nama_penuh as name, p.no_telefon as notelefon, j.jawatan, g.gred, b.bahagian, bm.nama_bilik_mesyuarat
                                                FROM pengguna p
                                                JOIN tbilik_penyelaras bp ON p.id = bp.idpenyelaras
                                                JOIN tbilik_mesyuarat bm ON bp.idbilik_mesyuarat = bm.id
                                                LEFT JOIN tbahagian b ON p.bahagian_id = b.id
                                                LEFT JOIN tjawatan j ON p.jawatan_id = j.id
                                                LEFT JOIN tgred g ON p.gred_id = g.id
                                                WHERE p.peranan = 'penyelaras'
                                                ORDER BY bm.nama_bilik_mesyuarat, p.nama_penuh";
                                        $result = $db->fetchAll($sql);
                                    } else {
                                        $sql = "SELECT p.id, p.nama_penuh as name, p.no_telefon as notelefon, j.jawatan, g.gred, b.bahagian
                                                FROM pengguna p
                                                JOIN tbilik_penyelaras bp ON p.id = bp.idpenyelaras
                                                LEFT JOIN tbahagian b ON p.bahagian_id = b.id
                                                LEFT JOIN tjawatan j ON p.jawatan_id = j.id
                                                LEFT JOIN tgred g ON p.gred_id = g.id
                                                WHERE bp.idbilik_mesyuarat = ? AND p.peranan = 'penyelaras'
                                                ORDER BY p.nama_penuh";
                                        $result = $db->fetchAll($sql, [$bilik_id]);
                                    }

                                    if (count($result) > 0) {
                                        $no = 1;
                                        foreach ($result as $row) {
                                            echo "<tr>";
                                            echo "<td>" . $no++ . "</td>";
                                            echo "<td>" . htmlspecialchars($row['name']) . "</td>";
                                            echo "<td>" . htmlspecialchars($row['jawatan'] ?? '-') . "</td>";
                                            echo "<td>" . htmlspecialchars($row['gred'] ?? '-') . "</td>";
                                            echo "<td>" . htmlspecialchars($row['bahagian'] ?? '-') . "</td>";
                                            echo "<td>" . htmlspecialchars($row['notelefon'] ?? '-') . "</td>";
                                            if ($bilik_id == 'all') {
                                                echo "<td>" . htmlspecialchars($row['nama_bilik_mesyuarat']) . "</td>";
                                            }
                                            echo "</tr>";
                                        }
                                    } else {
                                        $colspan = ($bilik_id == 'all') ? 7 : 6;
                                        echo "<tr><td colspan='$colspan' class='text-center'>Tiada penyelaras dijumpai</td></tr>";
                                    }
                                    ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Jadual Mesyuarat Content -->
                <?php if ($activeMenu == 'mesyuarat'): ?>
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="bi bi-calendar3"></i> Jadual Mesyuarat</h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" class="mb-4">
                            <input type="hidden" name="activeMenu" value="mesyuarat">
                            <div class="row">
                                <div class="col-md-4">
                                    <select name="bilik_id" class="form-select" required>
                                        <option value="">Pilih Bilik Mesyuarat</option>
                                        <?php
                                        $sql_bilik = "SELECT DISTINCT bm.id, bm.nama_bilik_mesyuarat
                                                     FROM tbilik_mesyuarat bm
                                                     JOIN tbilik_penyelaras bp ON bm.id = bp.idbilik_mesyuarat
                                                     WHERE bp.idpenyelaras = ?
                                                     ORDER BY bm.nama_bilik_mesyuarat";
                                        $result_bilik = $db->fetchAll($sql_bilik, [$numeric_user_id]);

                                        foreach ($result_bilik as $row_bilik) {
                                            $selected = (isset($_GET['bilik_id']) && $_GET['bilik_id'] == $row_bilik['id']) ? 'selected' : '';
                                            echo "<option value='" . $row_bilik['id'] . "' $selected>" .
                                                 htmlspecialchars($row_bilik['nama_bilik_mesyuarat']) . "</option>";
                                        }
                                        ?>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <select name="bulan" class="form-select" required>
                                        <option value="">Pilih Bulan</option>
                                        <?php
                                        $months = [
                                            1 => 'Januari', 2 => 'Februari', 3 => 'Mac', 4 => 'April',
                                            5 => 'Mei', 6 => 'Jun', 7 => 'Julai', 8 => 'Ogos',
                                            9 => 'September', 10 => 'Oktober', 11 => 'November', 12 => 'Disember'
                                        ];
                                        foreach ($months as $num => $name) {
                                            $selected = (isset($_GET['bulan']) && $_GET['bulan'] == $num) ? 'selected' : '';
                                            echo "<option value='$num' $selected>$name</option>";
                                        }
                                        ?>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <select name="tahun" class="form-select" required>
                                        <option value="">Pilih Tahun</option>
                                        <?php
                                        $current_year = date('Y');
                                        for ($year = $current_year - 1; $year <= $current_year + 2; $year++) {
                                            $selected = (isset($_GET['tahun']) && $_GET['tahun'] == $year) ? 'selected' : '';
                                            echo "<option value='$year' $selected>$year</option>";
                                        }
                                        ?>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="bi bi-search"></i> Jana
                                    </button>
                                </div>
                            </div>
                        </form>

                        <?php
                        if (isset($_GET['bilik_id']) && isset($_GET['bulan']) && isset($_GET['tahun'])) {
                            $bilik_id = (int)$_GET['bilik_id'];
                            $bulan = (int)$_GET['bulan'];
                            $tahun = (int)$_GET['tahun'];
                            
                            // Get room name
                            $sql_room = "SELECT nama_bilik_mesyuarat FROM tbilik_mesyuarat WHERE id = ?";
                            $room_data = $db->fetch($sql_room, [$bilik_id]);
                            $room_name = $room_data['nama_bilik_mesyuarat'];

                            // Get bookings
                            $sql = "SELECT t.*, p.nama_penuh as nama_pemohon,
                                    DATE_FORMAT(t.tarikh_mula, '%H:%i') as waktu_mula,
                                    DATE_FORMAT(t.tarikh_tamat, '%H:%i') as waktu_tamat,
                                    DATE(t.tarikh_mula) as tanggal
                                    FROM ttempahan t
                                    JOIN pengguna p ON t.idpemohon = p.id
                                    WHERE MONTH(t.tarikh_mula) = ?
                                    AND YEAR(t.tarikh_mula) = ?
                                    AND t.idbilik_mesyuarat = ?
                                    AND t.kelulusan = 'LULUS'
                                    ORDER BY t.tarikh_mula";

                            $result = $db->fetchAll($sql, [$bulan, $tahun, $bilik_id]);

                            $bookings = [];
                            foreach ($result as $row) {
                                $bookings[$row['tanggal']][] = $row;
                            }
                            ?>
                            
                            <div class="mt-4">
                                <h4 class="text-center mb-4">
                                    Jadual <?php echo $months[$bulan] . ' ' . $tahun; ?><br>
                                    <small class="text-muted"><?php echo htmlspecialchars($room_name); ?></small>
                                </h4>
                                
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead class="table-dark">
                                            <tr>
                                                <th width="20%">Tarikh</th>
                                                <th width="80%">Maklumat Tempahan</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php
                                            $num_days = cal_days_in_month(CAL_GREGORIAN, $bulan, $tahun);
                                            $day_names = [
                                                'Sunday' => 'Ahad', 'Monday' => 'Isnin', 'Tuesday' => 'Selasa',
                                                'Wednesday' => 'Rabu', 'Thursday' => 'Khamis', 'Friday' => 'Jumaat',
                                                'Saturday' => 'Sabtu'
                                            ];
                                            
                                            for ($day = 1; $day <= $num_days; $day++) {
                                                $date = sprintf('%04d-%02d-%02d', $tahun, $bulan, $day);
                                                $day_name = $day_names[date('l', strtotime($date))];
                                                ?>
                                                <tr>
                                                    <td class="fw-bold">
                                                        <?php echo $day . '/' . $bulan . '/' . $tahun; ?><br>
                                                        <small class="text-muted"><?php echo $day_name; ?></small>
                                                    </td>
                                                    <td>
                                                        <?php if (isset($bookings[$date])): ?>
                                                            <?php foreach ($bookings[$date] as $index => $booking): ?>
                                                                <div class="border-start border-primary border-3 ps-3 mb-2">
                                                                    <strong><?php echo ($index + 1) . '. ' . $booking['waktu_mula'] . ' - ' . $booking['waktu_tamat']; ?></strong><br>
                                                                    <strong>Mesyuarat:</strong> <?php echo htmlspecialchars($booking['tajuk_mesyuarat']); ?><br>
                                                                    <strong>Pengerusi:</strong> <?php echo htmlspecialchars($booking['pengerusi']); ?><br>
                                                                    <strong>Peserta:</strong> <?php echo $booking['bilangan_peserta']; ?> orang<br>
                                                                    <strong>Pemohon:</strong> <?php echo htmlspecialchars($booking['nama_pemohon']); ?>
                                                                </div>
                                                            <?php endforeach; ?>
                                                        <?php else: ?>
                                                            <span class="text-muted">Tiada tempahan</span>
                                                        <?php endif; ?>
                                                    </td>
                                                </tr>
                                                <?php
                                            }
                                            ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <?php
                            $stmt->close();
                        }
                        ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function luluskanTempahan(id) {
            if (confirm('Adakah anda pasti untuk meluluskan tempahan ini?')) {
                window.location.href = '../kawalan/lulus_tempahan.php?id=' + id + '&redirect=penyelaras';
            }
        }
        
        function tolakTempahan(id) {
            let ulasan = prompt('Sila masukkan ulasan untuk penolakan:', '');
            if (ulasan !== null && ulasan.trim() !== '') {
                window.location.href = '../kawalan/tolak_tempahan.php?id=' + id + '&ulasan=' + encodeURIComponent(ulasan) + '&redirect=penyelaras';
            }
        }
    </script>
</body>
</html>
