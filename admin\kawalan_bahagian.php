<?php
/**
 * Halaman Kawalan Bahagian
 * Sistem Tempahan Bilik Mesyuarat
 */

$tajuk_halaman = 'Kawalan Bahagian';
require_once '../config/sistem_config.php';

// Semak login dan peranan admin
perluLogin();
if (!isset($_SESSION['profile_id']) || $_SESSION['profile_id'] != 2) {
    header('Location: ../Menuutama/dashboard.php');
    exit;
}

$mesej_kejayaan = '';
$mesej_ralat = '';

// Proses form
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        $action = $_POST['action'];
        
        if ($action == 'tambah') {
            $bahagian = trim($_POST['bahagian']);
            $id = (int)$_POST['id'];
            $idptj = !empty($_POST['idptj']) ? (int)$_POST['idptj'] : null;
            
            if (empty($bahagian) || empty($id)) {
                $mesej_ralat = 'ID dan nama bahagian diperlukan.';
            } else {
                try {
                    // Semak jika ID sudah wujud
                    $existing = $db->fetch("SELECT id FROM tbahagian WHERE id = ?", [$id]);
                    if ($existing) {
                        $mesej_ralat = 'ID bahagian sudah wujud. Sila gunakan ID yang berbeza.';
                    } else {
                        $sql = "INSERT INTO tbahagian (id, bahagian, idptj) VALUES (?, ?, ?)";
                        $db->query($sql, [$id, $bahagian, $idptj]);
                        $mesej_kejayaan = 'Bahagian berjaya ditambah.';
                    }
                } catch (Exception $e) {
                    $mesej_ralat = 'Ralat: ' . $e->getMessage();
                }
            }
        } elseif ($action == 'edit') {
            $id = (int)$_POST['id'];
            $bahagian = trim($_POST['bahagian']);
            $idptj = !empty($_POST['idptj']) ? (int)$_POST['idptj'] : null;
            
            if (empty($bahagian)) {
                $mesej_ralat = 'Nama bahagian diperlukan.';
            } else {
                try {
                    $sql = "UPDATE tbahagian SET bahagian = ?, idptj = ? WHERE id = ?";
                    $db->query($sql, [$bahagian, $idptj, $id]);
                    $mesej_kejayaan = 'Bahagian berjaya dikemaskini.';
                } catch (Exception $e) {
                    $mesej_ralat = 'Ralat: ' . $e->getMessage();
                }
            }
        } elseif ($action == 'hapus') {
            $id = (int)$_POST['id'];
            
            try {
                // Semak jika bahagian digunakan oleh unit
                $unit_count = $db->fetch("SELECT COUNT(*) as jumlah FROM tunit WHERE idbahagian = ?", [$id])['jumlah'];

                // Semak jika bahagian digunakan oleh pengguna (dengan semakan kolum)
                $pengguna_count = 0;
                try {
                    $pengguna_count = $db->fetch("SELECT COUNT(*) as jumlah FROM pengguna WHERE bahagian_id = ?", [$id])['jumlah'];
                } catch (Exception $e) {
                    // Jika kolum bahagian_id tidak wujud, cuba kolum bahagian
                    try {
                        $pengguna_count = $db->fetch("SELECT COUNT(*) as jumlah FROM pengguna WHERE bahagian = ?", [$id])['jumlah'];
                    } catch (Exception $e2) {
                        // Abaikan jika kedua-dua kolum tidak wujud
                        $pengguna_count = 0;
                    }
                }

                // Semak jika bahagian digunakan oleh bilik mesyuarat
                $bilik_count = $db->fetch("SELECT COUNT(*) as jumlah FROM tbilik_mesyuarat WHERE bahagian = ?", [$id])['jumlah'];

                if ($unit_count > 0 || $pengguna_count > 0 || $bilik_count > 0) {
                    $mesej_ralat = "Bahagian tidak boleh dihapus kerana masih digunakan oleh $unit_count unit, $pengguna_count pengguna, dan $bilik_count bilik mesyuarat.";
                } else {
                    $sql = "DELETE FROM tbahagian WHERE id = ?";
                    $db->query($sql, [$id]);
                    $mesej_kejayaan = 'Bahagian berjaya dihapus.';
                }
            } catch (Exception $e) {
                $mesej_ralat = 'Ralat: ' . $e->getMessage();
            }
        }
    }
}

// Dapatkan senarai bahagian dengan statistik
try {
    // Cuba query dengan bahagian_id terlebih dahulu
    $senarai_bahagian = $db->fetchAll("
        SELECT b.*,
               (SELECT COUNT(*) FROM tunit WHERE idbahagian = b.id) as jumlah_unit,
               (SELECT COUNT(*) FROM pengguna WHERE bahagian_id = b.id) as jumlah_pengguna,
               (SELECT COUNT(*) FROM tbilik_mesyuarat WHERE bahagian = b.id) as jumlah_bilik
        FROM tbahagian b
        ORDER BY b.bahagian
    ");
} catch (Exception $e) {
    // Jika gagal, cuba dengan kolum bahagian
    try {
        $senarai_bahagian = $db->fetchAll("
            SELECT b.*,
                   (SELECT COUNT(*) FROM tunit WHERE idbahagian = b.id) as jumlah_unit,
                   (SELECT COUNT(*) FROM pengguna WHERE bahagian = b.id) as jumlah_pengguna,
                   (SELECT COUNT(*) FROM tbilik_mesyuarat WHERE bahagian = b.id) as jumlah_bilik
            FROM tbahagian b
            ORDER BY b.bahagian
        ");
    } catch (Exception $e2) {
        // Jika masih gagal, dapatkan data asas sahaja
        $senarai_bahagian = $db->fetchAll("
            SELECT b.*,
                   (SELECT COUNT(*) FROM tunit WHERE idbahagian = b.id) as jumlah_unit,
                   0 as jumlah_pengguna,
                   (SELECT COUNT(*) FROM tbilik_mesyuarat WHERE bahagian = b.id) as jumlah_bilik
            FROM tbahagian b
            ORDER BY b.bahagian
        ");
    }
}

require_once '../includes/header_sistem.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="bi bi-diagram-3 me-2"></i>Kawalan Bahagian</h2>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="../Menuutama/dashboard.php">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="kawalan.php">Kawalan</a></li>
                        <li class="breadcrumb-item active">Bahagian</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Mesej -->
    <?php if ($mesej_kejayaan): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="bi bi-check-circle me-2"></i><?= htmlspecialchars($mesej_kejayaan) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($mesej_ralat): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="bi bi-exclamation-triangle me-2"></i><?= htmlspecialchars($mesej_ralat) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- Form Tambah Bahagian -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-plus-circle me-2"></i>Tambah Bahagian Baru</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <input type="hidden" name="action" value="tambah">
                        
                        <div class="mb-3">
                            <label for="id" class="form-label">ID Bahagian</label>
                            <input type="number" class="form-control" id="id" name="id" required>
                            <div class="form-text">ID unik untuk bahagian (nombor)</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="bahagian" class="form-label">Nama Bahagian</label>
                            <input type="text" class="form-control" id="bahagian" name="bahagian" required>
                        </div>
                        <button type="submit" class="btn btn-success">
                            <i class="bi bi-plus-circle me-2"></i>Tambah Bahagian
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Senarai Bahagian -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-list me-2"></i>Senarai Bahagian</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($senarai_bahagian)): ?>
                        <div class="text-center py-4">
                            <i class="bi bi-inbox fs-1 text-muted"></i>
                            <p class="text-muted mt-2">Tiada bahagian dijumpai.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Nama Bahagian</th>
                                        <th>ID PTJ</th>
                                        <th>Unit</th>
                                        <th>Pengguna</th>
                                        <th>Bilik</th>
                                        <th>Tindakan</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($senarai_bahagian as $bahagian): ?>
                                        <tr>
                                            <td><?= $bahagian['id'] ?></td>
                                            <td><?= htmlspecialchars($bahagian['bahagian']) ?></td>
                                            <td><?= $bahagian['idptj'] ?? '-' ?></td>
                                            <td>
                                                <span class="badge bg-primary"><?= $bahagian['jumlah_unit'] ?></span>
                                            </td>
                                            <td>
                                                <span class="badge bg-info"><?= $bahagian['jumlah_pengguna'] ?></span>
                                            </td>
                                            <td>
                                                <span class="badge bg-warning"><?= $bahagian['jumlah_bilik'] ?></span>
                                            </td>
                                            <td>
                                                <button class="btn btn-sm btn-warning me-1" 
                                                        onclick="editBahagian(<?= $bahagian['id'] ?>, '<?= htmlspecialchars($bahagian['bahagian']) ?>', <?= $bahagian['idptj'] ?? 'null' ?>)">
                                                    <i class="bi bi-pencil"></i>
                                                </button>
                                                <button class="btn btn-sm btn-danger" 
                                                        onclick="hapusBahagian(<?= $bahagian['id'] ?>, '<?= htmlspecialchars($bahagian['bahagian']) ?>')">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Edit Bahagian -->
<div class="modal fade" id="editModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Bahagian</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="edit">
                    <input type="hidden" name="id" id="edit_id">
                    
                    <div class="mb-3">
                        <label for="edit_bahagian" class="form-label">Nama Bahagian</label>
                        <input type="text" class="form-control" id="edit_bahagian" name="bahagian" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_idptj" class="form-label">ID PTJ (Opsional)</label>
                        <input type="number" class="form-control" id="edit_idptj" name="idptj">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-warning">Kemaskini</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Hapus Bahagian -->
<div class="modal fade" id="hapusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Hapus Bahagian</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Adakah anda pasti ingin menghapus bahagian <strong id="hapus_nama"></strong>?</p>
                <p class="text-danger"><i class="bi bi-exclamation-triangle me-2"></i>Tindakan ini tidak boleh dibatalkan dan akan mempengaruhi unit, pengguna dan bilik yang berkaitan.</p>
            </div>
            <div class="modal-footer">
                <form method="POST">
                    <input type="hidden" name="action" value="hapus">
                    <input type="hidden" name="id" id="hapus_id">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-danger">Hapus</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function editBahagian(id, bahagian, idptj) {
    document.getElementById('edit_id').value = id;
    document.getElementById('edit_bahagian').value = bahagian;
    document.getElementById('edit_idptj').value = idptj || '';
    new bootstrap.Modal(document.getElementById('editModal')).show();
}

function hapusBahagian(id, bahagian) {
    document.getElementById('hapus_id').value = id;
    document.getElementById('hapus_nama').textContent = bahagian;
    new bootstrap.Modal(document.getElementById('hapusModal')).show();
}
</script>

<?php require_once '../includes/footer_sistem.php'; ?>
