<?php
/**
 * Fungsi-fungsi berkaitan peranan pengguna
 * Digunakan untuk mendapatkan maklumat peranan berdasarkan profile_id
 */

/**
 * Mendapatkan maklumat lengkap peranan pengguna
 * @param int $profile_id ID profil pengguna
 * @return array Maklumat peranan (display, icon, badge, description)
 */
function getPerananInfo($profile_id) {
    $peranan_info = [
        'display' => 'Pengguna',
        'icon' => 'bi-person-circle',
        'badge' => 'bg-secondary',
        'description' => 'Pengguna biasa sistem'
    ];
    
    switch ((int)$profile_id) {
        case 1:
            $peranan_info = [
                'display' => 'Pengguna',
                'icon' => 'bi-person-circle',
                'badge' => 'bg-primary',
                'description' => 'Pengguna biasa yang boleh membuat tempahan'
            ];
            break;
        case 2:
            $peranan_info = [
                'display' => 'Admin',
                'icon' => 'bi-shield-fill-check',
                'badge' => 'bg-danger',
                'description' => 'Pentadbir sistem dengan akses penuh'
            ];
            break;
        case 3:
            $peranan_info = [
                'display' => 'Penyelaras Bilik',
                'icon' => 'bi-gear-fill',
                'badge' => 'bg-warning',
                'description' => 'Penyelaras bilik mesyuarat'
            ];
            break;
        case 4:
            $peranan_info = [
                'display' => 'Ketua PTJ',
                'icon' => 'bi-star-fill',
                'badge' => 'bg-success',
                'description' => 'Ketua Pusat Tanggungjawab'
            ];
            break;
        case 9:
            $peranan_info = [
                'display' => 'Tetamu',
                'icon' => 'bi-person-badge',
                'badge' => 'bg-secondary',
                'description' => 'Pengguna tetamu'
            ];
            break;
    }
    
    return $peranan_info;
}

/**
 * Semak jika pengguna adalah admin
 * @param int $profile_id ID profil pengguna
 * @return bool True jika admin
 */
function isAdmin($profile_id = null) {
    if ($profile_id === null) {
        $profile_id = $_SESSION['profile_id'] ?? 0;
    }
    return (int)$profile_id === 2;
}

/**
 * Semak jika pengguna adalah penyelaras bilik
 * @param int $profile_id ID profil pengguna
 * @return bool True jika penyelaras bilik
 */
function isPenyelaras($profile_id = null) {
    if ($profile_id === null) {
        $profile_id = $_SESSION['profile_id'] ?? 0;
    }
    return (int)$profile_id === 3;
}

/**
 * Semak jika pengguna adalah ketua PTJ
 * @param int $profile_id ID profil pengguna
 * @return bool True jika ketua PTJ
 */
function isKetuaPTJ($profile_id = null) {
    if ($profile_id === null) {
        $profile_id = $_SESSION['profile_id'] ?? 0;
    }
    return (int)$profile_id === 4;
}

/**
 * Semak jika pengguna mempunyai kebenaran admin atau penyelaras
 * @param int $profile_id ID profil pengguna
 * @return bool True jika mempunyai kebenaran tinggi
 */
function hasElevatedPermissions($profile_id = null) {
    if ($profile_id === null) {
        $profile_id = $_SESSION['profile_id'] ?? 0;
    }
    return in_array((int)$profile_id, [2, 3, 4]); // Admin, Penyelaras, Ketua PTJ
}

/**
 * Dapatkan nama peranan ringkas
 * @param int $profile_id ID profil pengguna
 * @return string Nama peranan
 */
function getPerananName($profile_id) {
    $info = getPerananInfo($profile_id);
    return $info['display'];
}

/**
 * Dapatkan ikon peranan
 * @param int $profile_id ID profil pengguna
 * @return string Kelas ikon Bootstrap
 */
function getPerananIcon($profile_id) {
    $info = getPerananInfo($profile_id);
    return $info['icon'];
}

/**
 * Dapatkan kelas badge peranan
 * @param int $profile_id ID profil pengguna
 * @return string Kelas badge Bootstrap
 */
function getPerananBadge($profile_id) {
    $info = getPerananInfo($profile_id);
    return $info['badge'];
}

/**
 * Papar badge peranan dengan ikon
 * @param int $profile_id ID profil pengguna
 * @param bool $show_icon Tunjukkan ikon atau tidak
 * @return string HTML badge
 */
function displayPerananBadge($profile_id, $show_icon = true) {
    $info = getPerananInfo($profile_id);
    $icon = $show_icon ? '<i class="' . $info['icon'] . ' me-1"></i>' : '';
    return '<span class="badge ' . $info['badge'] . '">' . $icon . htmlspecialchars($info['display']) . '</span>';
}
?>
