<?php
/**
 * Halaman Periksa dan Perbaiki Struktur Database
 * Sistem Tempahan Bilik Mesyuarat
 */

$tajuk_halaman = 'Periksa Database';
require_once '../config/sistem_config.php';

// Semak login dan peranan admin
perluLogin();
if (!isset($_SESSION['profile_id']) || $_SESSION['profile_id'] != 2) {
    header('Location: ../Menuutama/dashboard.php');
    exit;
}

$mesej_kejayaan = '';
$mesej_ralat = '';
$status_database = [];

// Fungsi untuk memeriksa jika kolum wujud
function checkColumnExists($db, $table, $column) {
    try {
        $result = $db->fetchAll("SHOW COLUMNS FROM `$table` LIKE '$column'");
        return count($result) > 0;
    } catch (Exception $e) {
        return false;
    }
}

// Fungsi untuk memeriksa jika jadual wujud
function checkTableExists($db, $table) {
    try {
        $result = $db->fetchAll("SHOW TABLES LIKE '$table'");
        return count($result) > 0;
    } catch (Exception $e) {
        return false;
    }
}

// Proses perbaikan database
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['perbaiki_database'])) {
    try {
        // Tambah kolum kemudahan jika tidak wujud
        if (!checkColumnExists($db, 'tbilik_mesyuarat', 'kemudahan')) {
            $db->query("ALTER TABLE tbilik_mesyuarat ADD COLUMN kemudahan JSON AFTER penerangan");
            $mesej_kejayaan .= "Kolum 'kemudahan' berjaya ditambah ke jadual tbilik_mesyuarat.<br>";
        }
        
        // Tambah kolum bahagian_id jika tidak wujud
        if (!checkColumnExists($db, 'pengguna', 'bahagian_id')) {
            $db->query("ALTER TABLE pengguna ADD COLUMN bahagian_id INT AFTER no_telefon");
            $mesej_kejayaan .= "Kolum 'bahagian_id' berjaya ditambah ke jadual pengguna.<br>";
        }
        
        // Tambah kolum unit_id jika tidak wujud
        if (!checkColumnExists($db, 'pengguna', 'unit_id')) {
            $db->query("ALTER TABLE pengguna ADD COLUMN unit_id INT AFTER bahagian_id");
            $mesej_kejayaan .= "Kolum 'unit_id' berjaya ditambah ke jadual pengguna.<br>";
        }
        
        // Tambah kolum jawatan_id jika tidak wujud
        if (!checkColumnExists($db, 'pengguna', 'jawatan_id')) {
            $db->query("ALTER TABLE pengguna ADD COLUMN jawatan_id INT AFTER unit_id");
            $mesej_kejayaan .= "Kolum 'jawatan_id' berjaya ditambah ke jadual pengguna.<br>";
        }
        
        // Tambah kolum gred_id jika tidak wujud
        if (!checkColumnExists($db, 'pengguna', 'gred_id')) {
            $db->query("ALTER TABLE pengguna ADD COLUMN gred_id INT AFTER jawatan_id");
            $mesej_kejayaan .= "Kolum 'gred_id' berjaya ditambah ke jadual pengguna.<br>";
        }
        
        if (empty($mesej_kejayaan)) {
            $mesej_kejayaan = "Semua kolum yang diperlukan sudah wujud dalam database.";
        }
        
    } catch (Exception $e) {
        $mesej_ralat = 'Ralat semasa memperbaiki database: ' . $e->getMessage();
    }
}

// Periksa status database
$status_database = [
    'jadual' => [
        'pengguna' => checkTableExists($db, 'pengguna'),
        'tbahagian' => checkTableExists($db, 'tbahagian'),
        'tunit' => checkTableExists($db, 'tunit'),
        'tbilik_mesyuarat' => checkTableExists($db, 'tbilik_mesyuarat'),
        'tjawatan' => checkTableExists($db, 'tjawatan'),
        'tgred' => checkTableExists($db, 'tgred'),
    ],
    'kolum' => [
        'pengguna_bahagian_id' => checkColumnExists($db, 'pengguna', 'bahagian_id'),
        'pengguna_unit_id' => checkColumnExists($db, 'pengguna', 'unit_id'),
        'pengguna_jawatan_id' => checkColumnExists($db, 'pengguna', 'jawatan_id'),
        'pengguna_gred_id' => checkColumnExists($db, 'pengguna', 'gred_id'),
        'bilik_kemudahan' => checkColumnExists($db, 'tbilik_mesyuarat', 'kemudahan'),
    ]
];

require_once '../includes/header_sistem.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="bi bi-database me-2"></i>Periksa Struktur Database</h2>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="../Menuutama/dashboard.php">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="kawalan.php">Kawalan</a></li>
                        <li class="breadcrumb-item active">Periksa Database</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Mesej -->
    <?php if ($mesej_kejayaan): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="bi bi-check-circle me-2"></i><?= $mesej_kejayaan ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($mesej_ralat): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="bi bi-exclamation-triangle me-2"></i><?= htmlspecialchars($mesej_ralat) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- Status Jadual -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-table me-2"></i>Status Jadual Database</h5>
                </div>
                <div class="card-body">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Jadual</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($status_database['jadual'] as $jadual => $wujud): ?>
                                <tr>
                                    <td><code><?= $jadual ?></code></td>
                                    <td>
                                        <?php if ($wujud): ?>
                                            <span class="badge bg-success">✓ Wujud</span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">✗ Tidak Wujud</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Status Kolum -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-columns me-2"></i>Status Kolum Database</h5>
                </div>
                <div class="card-body">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Kolum</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($status_database['kolum'] as $kolum => $wujud): ?>
                                <tr>
                                    <td><code><?= str_replace('_', '.', $kolum) ?></code></td>
                                    <td>
                                        <?php if ($wujud): ?>
                                            <span class="badge bg-success">✓ Wujud</span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">✗ Tidak Wujud</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Butang Perbaikan -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-tools me-2"></i>Perbaikan Database</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        <strong>Nota:</strong> Butang ini akan menambah kolum yang hilang ke dalam database. 
                        Pastikan anda membuat backup database sebelum menjalankan perbaikan.
                    </div>
                    
                    <form method="POST">
                        <button type="submit" name="perbaiki_database" class="btn btn-warning" 
                                onclick="return confirm('Adakah anda pasti ingin memperbaiki struktur database? Pastikan anda sudah membuat backup.')">
                            <i class="bi bi-wrench me-2"></i>Perbaiki Struktur Database
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Panduan -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-question-circle me-2"></i>Panduan</h5>
                </div>
                <div class="card-body">
                    <h6>Kolum Yang Diperlukan:</h6>
                    <ul>
                        <li><strong>pengguna.bahagian_id</strong> - Untuk menghubungkan pengguna dengan bahagian</li>
                        <li><strong>pengguna.unit_id</strong> - Untuk menghubungkan pengguna dengan unit</li>
                        <li><strong>pengguna.jawatan_id</strong> - Untuk menghubungkan pengguna dengan jawatan</li>
                        <li><strong>pengguna.gred_id</strong> - Untuk menghubungkan pengguna dengan gred</li>
                        <li><strong>tbilik_mesyuarat.kemudahan</strong> - Untuk menyimpan kemudahan bilik dalam format JSON</li>
                    </ul>
                    
                    <h6 class="mt-3">Jika Masalah Berterusan:</h6>
                    <ol>
                        <li>Pastikan database <code>sistem_tempahan_bilik</code> wujud</li>
                        <li>Jalankan skrip setup database dari folder <code>database/</code></li>
                        <li>Periksa konfigurasi database dalam <code>config/sistem_config.php</code></li>
                    </ol>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../includes/footer_sistem.php'; ?>
