<?php
// Test file to check penyelaras functionality
session_start();

// Set test session data
$_SESSION['pengguna_id'] = 1; // Assuming user ID 1 exists

// Database connection
require_once 'config/database.php';

echo "<h1>Testing Penyelaras Database Connections</h1>";

try {
    // Test database connection
    echo "<h2>1. Database Connection Test</h2>";
    $test_query = "SELECT COUNT(*) as count FROM pengguna";
    $result = $db->fetch($test_query);
    echo "<p style='color: green;'>✓ Database connection successful. Total users: " . $result['count'] . "</p>";
    
    // Test pengguna table structure
    echo "<h2>2. Pengguna Table Structure Test</h2>";
    $columns_query = "SHOW COLUMNS FROM pengguna";
    $columns = $db->fetchAll($columns_query);
    echo "<p style='color: green;'>✓ Pengguna table columns:</p>";
    echo "<ul>";
    foreach ($columns as $column) {
        echo "<li>" . $column['Field'] . " (" . $column['Type'] . ")</li>";
    }
    echo "</ul>";
    
    // Test ttempahan table
    echo "<h2>3. Tempahan Table Test</h2>";
    $tempahan_query = "SELECT COUNT(*) as count FROM ttempahan";
    $tempahan_result = $db->fetch($tempahan_query);
    echo "<p style='color: green;'>✓ Tempahan table accessible. Total bookings: " . $tempahan_result['count'] . "</p>";
    
    // Test tbilik_mesyuarat table
    echo "<h2>4. Bilik Mesyuarat Table Test</h2>";
    $bilik_query = "SELECT COUNT(*) as count FROM tbilik_mesyuarat";
    $bilik_result = $db->fetch($bilik_query);
    echo "<p style='color: green;'>✓ Bilik mesyuarat table accessible. Total rooms: " . $bilik_result['count'] . "</p>";
    
    // Test tbilik_penyelaras table
    echo "<h2>5. Bilik Penyelaras Table Test</h2>";
    $penyelaras_query = "SELECT COUNT(*) as count FROM tbilik_penyelaras";
    $penyelaras_result = $db->fetch($penyelaras_query);
    echo "<p style='color: green;'>✓ Bilik penyelaras table accessible. Total assignments: " . $penyelaras_result['count'] . "</p>";
    
    // Test sample query from penyelaras.php
    echo "<h2>6. Sample Penyelaras Query Test</h2>";
    $sample_query = "SELECT t.*, p.nama_penuh as nama_pemohon, bm.nama_bilik_mesyuarat,
                     DATE_FORMAT(t.tarikh_mula, '%d/%m/%Y %H:%i') as tarikh_mula_formatted,
                     DATE_FORMAT(t.tarikh_tamat, '%d/%m/%Y %H:%i') as tarikh_tamat_formatted,
                     DATE_FORMAT(t.tarikh_mohon, '%d/%m/%Y') as tarikh_mohon_formatted
                     FROM ttempahan t
                     JOIN pengguna p ON t.idpemohon = p.id
                     JOIN tbilik_mesyuarat bm ON t.idbilik_mesyuarat = bm.id
                     JOIN tbilik_penyelaras bp ON bm.id = bp.idbilik_mesyuarat
                     WHERE bp.idpenyelaras = ?
                     ORDER BY t.tarikh_mula DESC
                     LIMIT 5";
    
    $sample_result = $db->fetchAll($sample_query, [1]); // Test with user ID 1
    echo "<p style='color: green;'>✓ Sample query executed successfully. Found " . count($sample_result) . " bookings.</p>";
    
    if (count($sample_result) > 0) {
        echo "<h3>Sample booking data:</h3>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Pemohon</th><th>Bilik</th><th>Tajuk</th><th>Status</th></tr>";
        foreach ($sample_result as $row) {
            echo "<tr>";
            echo "<td>" . $row['id'] . "</td>";
            echo "<td>" . htmlspecialchars($row['nama_pemohon']) . "</td>";
            echo "<td>" . htmlspecialchars($row['nama_bilik_mesyuarat']) . "</td>";
            echo "<td>" . htmlspecialchars($row['tajuk_mesyuarat']) . "</td>";
            echo "<td>" . $row['kelulusan'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h2>7. All Tests Completed Successfully!</h2>";
    echo "<p style='color: green; font-weight: bold;'>✓ The penyelaras.php file should now work correctly.</p>";
    echo "<p><a href='penyelaras/penyelaras.php'>Click here to test the actual penyelaras page</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error: " . $e->getMessage() . "</p>";
    echo "<p>Please check your database configuration and table structure.</p>";
}
?>
