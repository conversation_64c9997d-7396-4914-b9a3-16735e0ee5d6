<?php
/**
 * Halaman Pengurusan Pengguna
 * Sistem Tempahan Bilik Mesyuarat
 */

$tajuk_halaman = 'Pengurusan Pengguna';
require_once '../config/sistem_config.php';

// Semak login dan peranan admin
perluLogin();
if (!isset($_SESSION['profile_id']) || $_SESSION['profile_id'] != 2) {
    header('Location: ../Menuutama/dashboard.php');
    exit;
}

$mesej_kejayaan = '';
$mesej_ralat = '';
$mesej_amaran = '';

// Fungsi untuk memeriksa jika kolum wujud
function checkColumnExists($db, $table, $column) {
    try {
        $result = $db->fetchAll("SHOW COLUMNS FROM `$table` LIKE '$column'");
        return count($result) > 0;
    } catch (Exception $e) {
        return false;
    }
}

// Periksa jika kolum yang diperlukan wujud
$columns_exist = [
    'emel' => checkColumnExists($db, 'pengguna', 'emel'),
    'bahagian_id' => checkColumnExists($db, 'pengguna', 'bahagian_id'),
    'unit_id' => checkColumnExists($db, 'pengguna', 'unit_id'),
    'jawatan_id' => checkColumnExists($db, 'pengguna', 'jawatan_id'),
    'gred_id' => checkColumnExists($db, 'pengguna', 'gred_id')
];

// Proses CRUD operations
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        $action = $_POST['action'];
        
        try {
            if ($action == 'delete') {
                $user_id = $_POST['user_id'];
                $sql = "DELETE FROM pengguna WHERE id = ?";
                $db->query($sql, [$user_id]);
                $mesej_kejayaan = 'Pengguna berjaya dipadam.';
            }
            elseif ($action == 'edit') {
                $user_id = $_POST['user_id'];
                $nama_penuh = $_POST['nama_penuh'];
                $nokp = $_POST['nokp'];
                $emel = trim($_POST['emel'] ?? '');
                $no_telefon = $_POST['no_telefon'];
                
                // Check for duplicate email if not empty
                if (!empty($emel)) {
                    $existing_email = $db->fetch("SELECT id FROM pengguna WHERE emel = ? AND id != ?", [$emel, $user_id]);
                    if ($existing_email) {
                        throw new Exception('Emel sudah digunakan oleh pengguna lain.');
                    }
                }
                
                $bahagian_id = !empty($_POST['bahagian_id']) ? $_POST['bahagian_id'] : null;
                $unit_id = !empty($_POST['unit_id']) ? $_POST['unit_id'] : null;
                $jawatan_id = !empty($_POST['jawatan_id']) ? $_POST['jawatan_id'] : null;
                $gred_id = !empty($_POST['gred_id']) ? $_POST['gred_id'] : null;
                $profile_id = $_POST['profile_id'];
                $kata_laluan_baru = $_POST['kata_laluan_baru'] ?? '';
                
                // Prepare base parameters
                $update_fields = ["nama_penuh = ?", "nokp = ?", "no_telefon = ?", "profile_id = ?"];
                $params = [$nama_penuh, $nokp, $no_telefon, $profile_id];
                
                // Add email if column exists and not empty
                if ($columns_exist['emel']) {
                    if (!empty($emel)) {
                        $update_fields[] = "emel = ?";
                        $params[] = $emel;
                    } else {
                        $update_fields[] = "emel = NULL";
                    }
                }
                
                // Add optional fields if they exist
                if ($columns_exist['bahagian_id']) {
                    $update_fields[] = "bahagian_id = ?";
                    $params[] = $bahagian_id;
                }
                
                if ($columns_exist['unit_id']) {
                    $update_fields[] = "unit_id = ?";
                    $params[] = $unit_id;
                }
                
                if ($columns_exist['jawatan_id']) {
                    $update_fields[] = "jawatan_id = ?";
                    $params[] = $jawatan_id;
                }
                
                if ($columns_exist['gred_id']) {
                    $update_fields[] = "gred_id = ?";
                    $params[] = $gred_id;
                }
                
                // Add password if provided
                if (!empty($kata_laluan_baru)) {
                    $kata_laluan_hash = password_hash($kata_laluan_baru, PASSWORD_DEFAULT);
                    $update_fields[] = "kata_laluan = ?";
                    $params[] = $kata_laluan_hash;
                }
                
                $params[] = $user_id; // WHERE clause parameter
                
                $sql = "UPDATE pengguna SET " . implode(", ", $update_fields) . " WHERE id = ?";
                $db->query($sql, $params);
                
                if (!empty($kata_laluan_baru)) {
                    $mesej_kejayaan = 'Maklumat pengguna dan kata laluan berjaya dikemaskini.';
                } else {
                    $mesej_kejayaan = 'Maklumat pengguna berjaya dikemaskini.';
                }
            }
        } catch (Exception $e) {
            $mesej_ralat = 'Ralat: ' . $e->getMessage();
        }
    }
}

// Dapatkan senarai pengguna dengan maklumat lengkap
try {
    // First, let's check for duplicates and get unique users only
    $sql = "SELECT DISTINCT p.id, p.nama_penuh, p.nokp, p.no_telefon, p.profile_id";
    
    if ($columns_exist['emel']) {
        $sql .= ", p.emel";
    }
    if ($columns_exist['bahagian_id']) {
        $sql .= ", p.bahagian_id";
    }
    if ($columns_exist['unit_id']) {
        $sql .= ", p.unit_id";
    }
    if ($columns_exist['jawatan_id']) {
        $sql .= ", p.jawatan_id";
    }
    if ($columns_exist['gred_id']) {
        $sql .= ", p.gred_id";
    }
    
    $sql .= " FROM pengguna p GROUP BY p.id ORDER BY p.nama_penuh";
    
    $senarai_pengguna = $db->fetchAll($sql);
    
    // Debug: Check for actual duplicates in database
    $duplicate_check = $db->fetchAll("SELECT nokp, COUNT(*) as count FROM pengguna GROUP BY nokp HAVING COUNT(*) > 1");
    if (!empty($duplicate_check)) {
        error_log("Duplicate users found: " . print_r($duplicate_check, true));
    }
    
    // Get lookup data separately
    $bahagian_lookup = [];
    $unit_lookup = [];
    $jawatan_lookup = [];
    $gred_lookup = [];
    $profile_lookup = [];
    
    try {
        if ($columns_exist['bahagian_id']) {
            $bahagian_data = $db->fetchAll("SELECT id, bahagian FROM tbahagian");
            foreach ($bahagian_data as $row) {
                $bahagian_lookup[$row['id']] = $row['bahagian'];
            }
        }
        
        if ($columns_exist['unit_id']) {
            $unit_data = $db->fetchAll("SELECT id, unit FROM tunit");
            foreach ($unit_data as $row) {
                $unit_lookup[$row['id']] = $row['unit'];
            }
        }
        
        if ($columns_exist['jawatan_id']) {
            $jawatan_data = $db->fetchAll("SELECT id, jawatan FROM tjawatan");
            foreach ($jawatan_data as $row) {
                $jawatan_lookup[$row['id']] = $row['jawatan'];
            }
        }
        
        if ($columns_exist['gred_id']) {
            $gred_data = $db->fetchAll("SELECT id, gred FROM tgred");
            foreach ($gred_data as $row) {
                $gred_lookup[$row['id']] = $row['gred'];
            }
        }
        
        $profile_data = $db->fetchAll("SELECT id, name FROM profile");
        foreach ($profile_data as $row) {
            $profile_lookup[$row['id']] = $row['name'];
        }
    } catch (Exception $e) {
        error_log("Error loading lookup data: " . $e->getMessage());
    }
    
    // Process each user and add lookup values
    $processed_users = [];
    $seen_ids = [];
    
    foreach ($senarai_pengguna as $pengguna) {
        // Skip if we've already processed this user ID
        if (in_array($pengguna['id'], $seen_ids)) {
            break; // Changed from 'continue' to 'break'
        }
        $seen_ids[] = $pengguna['id'];
        
        // Add missing columns with defaults
        if (!$columns_exist['emel']) {
            $pengguna['emel'] = '';
        }
        
        if ($columns_exist['bahagian_id']) {
            $pengguna['bahagian'] = $bahagian_lookup[$pengguna['bahagian_id']] ?? ' - ';
        } else {
            $pengguna['bahagian_id'] = null;
            $pengguna['bahagian'] = ' - ';
        }
        
        if ($columns_exist['unit_id']) {
            $pengguna['unit'] = $unit_lookup[$pengguna['unit_id']] ?? ' - ';
        } else {
            $pengguna['unit_id'] = null;
            $pengguna['unit'] = ' - ';
        }
        
        if ($columns_exist['jawatan_id']) {
            $pengguna['jawatan'] = $jawatan_lookup[$pengguna['jawatan_id']] ?? ' - ';
        } else {
            $pengguna['jawatan_id'] = null;
            $pengguna['jawatan'] = ' - ';
        }
        
        if ($columns_exist['gred_id']) {
            $pengguna['gred'] = $gred_lookup[$pengguna['gred_id']] ?? ' - ';
        } else {
            $pengguna['gred_id'] = null;
            $pengguna['gred'] = ' - ';
        }
        
        $pengguna['peranan'] = $profile_lookup[$pengguna['profile_id']] ?? ' - ';
        
        $processed_users[] = $pengguna;
    }
    
    $senarai_pengguna = $processed_users;
    
    // Dapatkan senarai untuk dropdown
    $senarai_bahagian = $db->fetchAll("SELECT id, bahagian FROM tbahagian ORDER BY bahagian");
    $senarai_unit = $db->fetchAll("SELECT id, unit, idbahagian as bahagian_id FROM tunit ORDER BY unit");
    $senarai_jawatan = $db->fetchAll("SELECT id, jawatan FROM tjawatan ORDER BY jawatan");
    $senarai_gred = $db->fetchAll("SELECT id, gred FROM tgred ORDER BY gred");
    $senarai_profile = $db->fetchAll("SELECT id, name FROM profile WHERE id IN (1,2,3,4) ORDER BY id");
    
} catch (Exception $e) {
    $mesej_ralat = 'Ralat mendapatkan data: ' . $e->getMessage();
    $senarai_pengguna = [];
    $senarai_bahagian = [];
    $senarai_unit = [];
    $senarai_jawatan = [];
    $senarai_gred = [];
    $senarai_profile = [];
}

// Periksa jika ada kolum yang tidak wujud dan beri amaran
$missing_columns = [];
foreach ($columns_exist as $column => $exists) {
    if (!$exists) {
        $missing_columns[] = $column;
    }
}

if (!empty($missing_columns)) {
    $mesej_amaran = 'Struktur database tidak lengkap. Kolum yang hilang: ' . implode(', ', $missing_columns) .
                   '. <a href="periksa_database.php" class="alert-link">Klik di sini untuk memperbaiki database</a>.';
}

require_once '../includes/header_sistem.php';
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0"><?= $tajuk_halaman ?></h1>
                <p class="text-muted mb-0">Pengurusan pengguna sistem</p>
            </div>
        </div>
    </div>
</div>

<?php if ($mesej_kejayaan): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="bi bi-check-circle me-2"></i><?= $mesej_kejayaan ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($mesej_ralat): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="bi bi-exclamation-triangle me-2"></i><?= $mesej_ralat ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="bi bi-people me-2"></i>Senarai Pengguna
        </h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>No. KP</th>
                        <th>Nama Penuh</th>
                        <th>Bahagian</th>
                        <th>Unit</th>
                        <th>No. Telefon</th>
                        <th>Profil</th>
                        <th>Tindakan</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($senarai_pengguna)): ?>
                        <tr>
                            <td colspan="7" class="text-center text-muted">Tiada pengguna dijumpai</td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($senarai_pengguna as $pengguna): ?>
                            <tr>
                                <td><?= htmlspecialchars($pengguna['nokp']) ?></td>
                                <td><?= htmlspecialchars($pengguna['nama_penuh']) ?></td>
                                <td><?= htmlspecialchars($pengguna['bahagian'] ?? '-') ?></td>
                                <td><?= htmlspecialchars($pengguna['unit'] ?? '-') ?></td>
                                <td><?= htmlspecialchars($pengguna['no_telefon'] ?? '-') ?></td>
                                <td>
                                    <?php
                                    $badge_class = '';
                                    switch($pengguna['profile_id']) {
                                        case 1: $badge_class = 'bg-primary'; break;
                                        case 2: $badge_class = 'bg-danger'; break;
                                        case 3: $badge_class = 'bg-warning'; break;
                                        case 4: $badge_class = 'bg-success'; break;
                                    }
                                    ?>
                                    <span class="badge <?= $badge_class ?>"><?= htmlspecialchars($pengguna['peranan']) ?></span>
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary me-1" onclick="editPengguna(<?= $pengguna['id'] ?>)">
                                        <i class="bi bi-pencil"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger" onclick="deletePengguna(<?= $pengguna['id'] ?>, '<?= htmlspecialchars($pengguna['nama_penuh']) ?>')">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Modal View Pengguna -->
<div class="modal fade" id="viewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Maklumat Pengguna</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Nama Penuh:</strong></td>
                                <td id="view_nama_penuh"></td>
                            </tr>
                            <tr>
                                <td><strong>No. KP:</strong></td>
                                <td id="view_nokp"></td>
                            </tr>
                            <tr>
                                <td><strong>Emel:</strong></td>
                                <td id="view_emel"></td>
                            </tr>
                            <tr>
                                <td><strong>No. Telefon:</strong></td>
                                <td id="view_no_telefon"></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Bahagian:</strong></td>
                                <td id="view_bahagian"></td>
                            </tr>
                            <tr>
                                <td><strong>Unit:</strong></td>
                                <td id="view_unit"></td>
                            </tr>
                            <tr>
                                <td><strong>Jawatan:</strong></td>
                                <td id="view_jawatan"></td>
                            </tr>
                            <tr>
                                <td><strong>Gred:</strong></td>
                                <td id="view_gred"></td>
                            </tr>
                            <tr>
                                <td><strong>Profil:</strong></td>
                                <td id="view_profil"></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal Edit Pengguna -->
<div class="modal fade" id="editModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <form method="POST">
                <div class="modal-header">
                    <h5 class="modal-title">Edit Pengguna</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="action" value="edit">
                    <input type="hidden" name="user_id" id="edit_user_id">
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Nama Penuh *</label>
                            <input type="text" class="form-control" name="nama_penuh" id="edit_nama_penuh" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label">No. KP *</label>
                            <input type="text" class="form-control" name="nokp" id="edit_nokp" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Emel</label>
                            <input type="email" class="form-control" name="emel" id="edit_emel">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label">No. Telefon</label>
                            <input type="text" class="form-control" name="no_telefon" id="edit_no_telefon">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Bahagian</label>
                            <select class="form-select" name="bahagian_id" id="edit_bahagian_id">
                                <option value="">Pilih Bahagian</option>
                                <?php foreach ($senarai_bahagian as $bahagian): ?>
                                    <option value="<?= $bahagian['id'] ?>"><?= htmlspecialchars($bahagian['bahagian']) ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Unit</label>
                            <select class="form-select" name="unit_id" id="edit_unit_id">
                                <option value="">Pilih Unit</option>
                                <?php foreach ($senarai_unit as $unit): ?>
                                    <option value="<?= $unit['id'] ?>" data-bahagian="<?= $unit['bahagian_id'] ?>"><?= htmlspecialchars($unit['unit']) ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Jawatan</label>
                            <select class="form-select" name="jawatan_id" id="edit_jawatan_id">
                                <option value="">Pilih Jawatan</option>
                                <?php foreach ($senarai_jawatan as $jawatan): ?>
                                    <option value="<?= $jawatan['id'] ?>"><?= htmlspecialchars($jawatan['jawatan']) ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Gred</label>
                            <select class="form-select" name="gred_id" id="edit_gred_id">
                                <option value="">Pilih Gred</option>
                                <?php foreach ($senarai_gred as $gred): ?>
                                    <option value="<?= $gred['id'] ?>"><?= htmlspecialchars($gred['gred']) ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Profil *</label>
                            <select class="form-select" name="profile_id" id="edit_profile_id" required>
                                <?php foreach ($senarai_profile as $profile): ?>
                                    <option value="<?= $profile['id'] ?>"><?= htmlspecialchars($profile['name']) ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Kata Laluan Baru</label>
                            <input type="password" class="form-control" name="kata_laluan_baru" id="edit_kata_laluan_baru">
                            <div class="form-text">Biarkan kosong jika tidak mahu mengubah kata laluan</div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Kemaskini</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Delete Confirmation -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST">
                <div class="modal-header">
                    <h5 class="modal-title">Padam Pengguna</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="user_id" id="delete_user_id">
                    <p>Adakah anda pasti ingin memadam pengguna <strong id="delete_user_name"></strong>?</p>
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle me-2"></i>Tindakan ini tidak boleh dibatalkan.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-danger">Padam</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Data pengguna untuk JavaScript
const penggunaData = <?= json_encode($processed_users) ?>;

function editPengguna(userId) {
    const pengguna = penggunaData.find(p => p.id == userId);
    if (pengguna) {
        document.getElementById('edit_user_id').value = pengguna.id;
        document.getElementById('edit_nama_penuh').value = pengguna.nama_penuh;
        document.getElementById('edit_nokp').value = pengguna.nokp;
        document.getElementById('edit_emel').value = pengguna.emel || ''; // Fix email display
        document.getElementById('edit_no_telefon').value = pengguna.no_telefon || '';
        document.getElementById('edit_kata_laluan_baru').value = ''; // Clear password field
        document.getElementById('edit_profile_id').value = pengguna.profile_id;
        
        // Set bahagian dan unit
        const bahagianSelect = document.getElementById('edit_bahagian_id');
        const unitSelect = document.getElementById('edit_unit_id');
        
        // Reset unit options
        filterUnitOptions();
        
        // Set values after filtering
        setTimeout(() => {
            if (pengguna.bahagian_id) {
                bahagianSelect.value = pengguna.bahagian_id;
                filterUnitOptions();
                if (pengguna.unit_id) {
                    unitSelect.value = pengguna.unit_id;
                }
            }
        }, 100);
        
        new bootstrap.Modal(document.getElementById('editModal')).show();
    }
}

function deletePengguna(userId, namapengguna) {
    document.getElementById('delete_user_id').value = userId;
    document.getElementById('delete_user_name').textContent = namapengguna;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

function filterUnitOptions() {
    const bahagianId = document.getElementById('edit_bahagian_id').value;
    const unitSelect = document.getElementById('edit_unit_id');
    const unitOptions = unitSelect.querySelectorAll('option');
    
    unitOptions.forEach(option => {
        if (option.value === '') {
            option.style.display = 'block';
        } else {
            const unitBahagianId = option.getAttribute('data-bahagian');
            option.style.display = (!bahagianId || unitBahagianId === bahagianId) ? 'block' : 'none';
        }
    });
    
    unitSelect.value = '';
}

// Event listener untuk filter unit berdasarkan bahagian
document.getElementById('edit_bahagian_id').addEventListener('change', filterUnitOptions);
</script>

<?php require_once '../includes/footer_sistem.php'; ?>

























