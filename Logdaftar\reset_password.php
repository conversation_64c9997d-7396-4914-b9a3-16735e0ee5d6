<?php
/**
 * Reset Password - Untuk Testing
 * Sistem Tempahan Bilik Mesyuarat
 */

// Konfigurasi database
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'sistem_tempahan_bilik';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Hash kata laluan 'password'
    $password_hash = password_hash('password', PASSWORD_DEFAULT);
    
    // Update semua pengguna dengan kata laluan baru
    $sql = "UPDATE pengguna SET kata_laluan = ? WHERE id IN (1, 2, 3, 4, 5)";
    $stmt = $pdo->prepare($sql);
    $result = $stmt->execute([$password_hash]);
    
    if ($result) {
        echo "<h2>✅ Kata Laluan Berjaya Direset!</h2>";
        echo "<p>Semua pengguna kini menggunakan kata laluan: <strong>password</strong></p>";
        
        // Papar senarai pengguna
        $sql_users = "SELECT p.nokp, p.nama_penuh, pr.name as profile_name 
                      FROM pengguna p 
                      JOIN profile pr ON p.profile_id = pr.id 
                      ORDER BY p.profile_id";
        $stmt = $pdo->prepare($sql_users);
        $stmt->execute();
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>Senarai Pengguna:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 10px;'>No. KP</th>";
        echo "<th style='padding: 10px;'>Nama</th>";
        echo "<th style='padding: 10px;'>Profile</th>";
        echo "<th style='padding: 10px;'>Kata Laluan</th>";
        echo "</tr>";
        
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td style='padding: 10px;'>" . $user['nokp'] . "</td>";
            echo "<td style='padding: 10px;'>" . $user['nama_penuh'] . "</td>";
            echo "<td style='padding: 10px;'>" . $user['profile_name'] . "</td>";
            echo "<td style='padding: 10px;'><strong>password</strong></td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<br><div style='text-align: center;'>";
        echo "<a href='log_masuk.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>Test Log Masuk</a>";
        echo "</div>";
        
    } else {
        echo "<h2>❌ Ralat Reset Kata Laluan</h2>";
    }
    
} catch (Exception $e) {
    echo "<h2>❌ Ralat Database</h2>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Reset Password</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { margin: 20px 0; }
        th, td { text-align: left; }
    </style>
</head>
<body>
</body>
</html>
