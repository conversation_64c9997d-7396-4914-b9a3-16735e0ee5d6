<?php
require_once '../config/config.php';
require_once '../classes/Booking.php';

// Check if user is logged in
if (!isLoggedIn()) {
    http_response_code(403);
    echo json_encode(['error' => 'Access denied']);
    exit;
}

$room_id = (int)($_GET['room_id'] ?? 0);
$date = $_GET['date'] ?? '';

if (!$room_id || !$date) {
    echo json_encode(['error' => 'Missing parameters']);
    exit;
}

$booking = new Booking();
$bookings = $booking->getRoomBookings($room_id, $date);

// Format the response
$formatted_bookings = [];
foreach ($bookings as $booking_item) {
    $formatted_bookings[] = [
        'id' => $booking_item['id'],
        'start_time' => formatTime($booking_item['start_time']),
        'end_time' => formatTime($booking_item['end_time']),
        'purpose' => htmlspecialchars($booking_item['purpose']),
        'full_name' => htmlspecialchars($booking_item['full_name']),
        'status' => $booking_item['status']
    ];
}

header('Content-Type: application/json');
echo json_encode($formatted_bookings);
?>
