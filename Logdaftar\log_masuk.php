<?php
/**
 * Halaman Log Masuk - Sistem Sebenar
 * Sistem Tempahan Bilik Mesyuarat
 */

session_start();

// Jika sudah log masuk, redirect ke dashboard
if (isset($_SESSION['pengguna_id'])) {
    header('Location: ../Menuutama/dashboard.php');
    exit;
}

// Konfigurasi database
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'sistem_tempahan_bilik';

$mesej_ralat = '';

// Proses log masuk
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $nokp = trim($_POST['nokp']);
    $kata_laluan = $_POST['kata_laluan'];
    
    if (empty($nokp) || empty($kata_laluan)) {
        $mesej_ralat = 'Sila masukkan No. KP dan kata laluan.';
    } else {
        try {
            $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            $sql = "SELECT p.id, p.nokp, p.kata_laluan, p.nama_penuh, p.emel, p.profile_id, p.status,
                           pr.name as profile_name, pr.peranan as profile_peranan
                    FROM pengguna p
                    JOIN profile pr ON p.profile_id = pr.id
                    WHERE p.nokp = ? AND p.status = 'aktif'";
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$nokp]);
            $pengguna = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($pengguna && password_verify($kata_laluan, $pengguna['kata_laluan'])) {
                // Set session
                $_SESSION['pengguna_id'] = $pengguna['id'];
                $_SESSION['nokp'] = $pengguna['nokp'];
                $_SESSION['nama_penuh'] = $pengguna['nama_penuh'];
                $_SESSION['emel'] = $pengguna['emel'];
                $_SESSION['profile_id'] = $pengguna['profile_id'];
                $_SESSION['profile_name'] = $pengguna['profile_name'];
                $_SESSION['profile_peranan'] = $pengguna['profile_peranan'];

                // Log aktiviti log masuk
                try {
                    require_once '../config/sistem_config.php';
                    logAktiviti($pengguna['id'], 'Log Masuk', 'Pengguna log masuk ke sistem');
                } catch (Exception $e) {
                    // Ignore error jika tidak dapat log aktiviti
                }

                // Redirect semua jenis user ke dashboard utama
                header('Location: ../Menuutama/dashboard.php');
                exit;
            } else {
                $mesej_ralat = 'No. KP atau kata laluan tidak betul.';
            }
        } catch (Exception $e) {
            $mesej_ralat = 'Ralat sistem. Sila cuba lagi.';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ms">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Log Masuk - Sistem Tempahan Bilik Mesyuarat</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            max-height: 100vh;
            display: flex;
            align-items: center;
            overflow: hidden;
        }
        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0;
        }
        .container {
            max-height: 100vh;
            overflow: hidden;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="card login-card border-0">
                    <div class="card-header login-header text-center py-3">
                        <i class="bi bi-building fs-2 mb-2"></i>
                        <h4 class="mb-0">Sistem Tempahan</h4>
                        <small class="mb-0">Bilik Mesyuarat</small>
                    </div>

                    <div class="card-body p-4">
                        <h5 class="text-center mb-3">Log Masuk</h5>
                        
                        <?php if (!empty($mesej_ralat)): ?>
                            <div class="alert alert-danger">
                                <i class="bi bi-exclamation-triangle me-2"></i><?= $mesej_ralat ?>
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST" action="">
                            <div class="mb-3">
                                <label for="nokp" class="form-label">
                                    <i class="bi bi-person-badge me-2"></i>No. Kad Pengenalan
                                </label>
                                <input type="text" class="form-control" id="nokp" name="nokp" 
                                       value="<?= htmlspecialchars($_POST['nokp'] ?? '') ?>" 
                                       placeholder="Contoh: 123456789012" required>
                                <small class="text-muted">Masukkan No. KP tanpa tanda '-'</small>
                            </div>
                            
                            <div class="mb-3">
                                <label for="kata_laluan" class="form-label">
                                    <i class="bi bi-lock me-2"></i>Kata Laluan
                                </label>
                                <input type="password" class="form-control" id="kata_laluan" name="kata_laluan"
                                       placeholder="Masukkan kata laluan" required>
                            </div>

                            <div class="d-grid mb-3">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-box-arrow-in-right me-2"></i>Log Masuk
                                </button>
                            </div>
                        </form>

                        <hr class="my-3">

                        <div class="text-center">
                            <small class="text-muted d-block mb-2">
                                <strong>Akaun Lalai:</strong> Admin: <code>123456789012</code> / <code>password</code>
                            </small>
                            <a href="daftar.php" class="small">Belum ada akaun? Daftar</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Validasi No. KP
        document.getElementById('nokp').addEventListener('input', function() {
            const nokp = this.value.replace(/\D/g, ''); // Buang semua bukan digit
            this.value = nokp;
        });
    </script>
</body>
</html>
