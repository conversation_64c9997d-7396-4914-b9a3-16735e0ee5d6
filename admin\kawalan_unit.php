<?php
/**
 * Halaman Kawalan Unit
 * Sistem Tempahan Bilik Mesyuarat
 */

$tajuk_halaman = 'Kawalan Unit';
require_once '../config/sistem_config.php';

// Semak login dan peranan admin
perluLogin();
if (!isset($_SESSION['profile_id']) || $_SESSION['profile_id'] != 2) {
    header('Location: ../Menuutama/dashboard.php');
    exit;
}

$mesej_kejayaan = '';
$mesej_ralat = '';

// Proses form
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        $action = $_POST['action'];
        
        if ($action == 'tambah') {
            $unit = trim($_POST['unit']);
            $idbahagian = (int)$_POST['idbahagian'];
            $id = (int)$_POST['id'];
            
            if (empty($unit) || empty($idbahagian) || empty($id)) {
                $mesej_ralat = 'Semua medan diperlukan.';
            } else {
                try {
                    // Semak jika ID sudah wujud
                    $existing = $db->fetch("SELECT id FROM tunit WHERE id = ?", [$id]);
                    if ($existing) {
                        $mesej_ralat = 'ID unit sudah wujud. Sila gunakan ID yang berbeza.';
                    } else {
                        $sql = "INSERT INTO tunit (id, unit, idbahagian) VALUES (?, ?, ?)";
                        $db->query($sql, [$id, $unit, $idbahagian]);
                        $mesej_kejayaan = 'Unit berjaya ditambah.';
                    }
                } catch (Exception $e) {
                    $mesej_ralat = 'Ralat: ' . $e->getMessage();
                }
            }
        } elseif ($action == 'edit') {
            $id = (int)$_POST['id'];
            $unit = trim($_POST['unit']);
            $idbahagian = (int)$_POST['idbahagian'];
            
            if (empty($unit) || empty($idbahagian)) {
                $mesej_ralat = 'Semua medan diperlukan.';
            } else {
                try {
                    $sql = "UPDATE tunit SET unit = ?, idbahagian = ? WHERE id = ?";
                    $db->query($sql, [$unit, $idbahagian, $id]);
                    $mesej_kejayaan = 'Unit berjaya dikemaskini.';
                } catch (Exception $e) {
                    $mesej_ralat = 'Ralat: ' . $e->getMessage();
                }
            }
        } elseif ($action == 'hapus') {
            $id = (int)$_POST['id'];
            
            try {
                // Semak jika unit digunakan oleh pengguna (dengan semakan kolum)
                $pengguna_count = 0;
                try {
                    $pengguna_count = $db->fetch("SELECT COUNT(*) as jumlah FROM pengguna WHERE unit_id = ?", [$id])['jumlah'];
                } catch (Exception $e) {
                    // Jika kolum unit_id tidak wujud, cuba kolum unit
                    try {
                        $pengguna_count = $db->fetch("SELECT COUNT(*) as jumlah FROM pengguna WHERE unit = ?", [$id])['jumlah'];
                    } catch (Exception $e2) {
                        // Abaikan jika kedua-dua kolum tidak wujud
                        $pengguna_count = 0;
                    }
                }

                if ($pengguna_count > 0) {
                    $mesej_ralat = "Unit tidak boleh dihapus kerana masih digunakan oleh $pengguna_count pengguna.";
                } else {
                    $sql = "DELETE FROM tunit WHERE id = ?";
                    $db->query($sql, [$id]);
                    $mesej_kejayaan = 'Unit berjaya dihapus.';
                }
            } catch (Exception $e) {
                $mesej_ralat = 'Ralat: ' . $e->getMessage();
            }
        }
    }
}

// Dapatkan senarai unit dengan bahagian
$senarai_unit = $db->fetchAll("
    SELECT u.*, b.bahagian 
    FROM tunit u 
    LEFT JOIN tbahagian b ON u.idbahagian = b.id 
    ORDER BY b.bahagian, u.unit
");

// Dapatkan senarai bahagian untuk dropdown
$senarai_bahagian = $db->fetchAll("SELECT * FROM tbahagian ORDER BY bahagian");

require_once '../includes/header_sistem.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="bi bi-building me-2"></i>Kawalan Unit</h2>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="../Menuutama/dashboard.php">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="kawalan.php">Kawalan</a></li>
                        <li class="breadcrumb-item active">Unit</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Mesej -->
    <?php if ($mesej_kejayaan): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="bi bi-check-circle me-2"></i><?= htmlspecialchars($mesej_kejayaan) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($mesej_ralat): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="bi bi-exclamation-triangle me-2"></i><?= htmlspecialchars($mesej_ralat) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- Form Tambah Unit -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-plus-circle me-2"></i>Tambah Unit Baru</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <input type="hidden" name="action" value="tambah">
                        
                        <div class="mb-3">
                            <label for="id" class="form-label">ID Unit</label>
                            <input type="number" class="form-control" id="id" name="id" required>
                            <div class="form-text">ID unik untuk unit (nombor)</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="unit" class="form-label">Nama Unit</label>
                            <input type="text" class="form-control" id="unit" name="unit" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="idbahagian" class="form-label">Bahagian</label>
                            <select class="form-select" id="idbahagian" name="idbahagian" required>
                                <option value="">Pilih Bahagian</option>
                                <?php foreach ($senarai_bahagian as $bahagian): ?>
                                    <option value="<?= $bahagian['id'] ?>"><?= htmlspecialchars($bahagian['bahagian']) ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-plus-circle me-2"></i>Tambah Unit
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Senarai Unit -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-list me-2"></i>Senarai Unit</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($senarai_unit)): ?>
                        <div class="text-center py-4">
                            <i class="bi bi-inbox fs-1 text-muted"></i>
                            <p class="text-muted mt-2">Tiada unit dijumpai.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Nama Unit</th>
                                        <th>Bahagian</th>
                                        <th>Tindakan</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($senarai_unit as $unit): ?>
                                        <tr>
                                            <td><?= $unit['id'] ?></td>
                                            <td><?= htmlspecialchars($unit['unit']) ?></td>
                                            <td><?= htmlspecialchars($unit['bahagian'] ?? 'Tiada Bahagian') ?></td>
                                            <td>
                                                <button class="btn btn-sm btn-warning me-1" 
                                                        onclick="editUnit(<?= $unit['id'] ?>, '<?= htmlspecialchars($unit['unit']) ?>', <?= $unit['idbahagian'] ?>)">
                                                    <i class="bi bi-pencil"></i>
                                                </button>
                                                <button class="btn btn-sm btn-danger" 
                                                        onclick="hapusUnit(<?= $unit['id'] ?>, '<?= htmlspecialchars($unit['unit']) ?>')">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Edit Unit -->
<div class="modal fade" id="editModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Unit</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="edit">
                    <input type="hidden" name="id" id="edit_id">
                    
                    <div class="mb-3">
                        <label for="edit_unit" class="form-label">Nama Unit</label>
                        <input type="text" class="form-control" id="edit_unit" name="unit" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_idbahagian" class="form-label">Bahagian</label>
                        <select class="form-select" id="edit_idbahagian" name="idbahagian" required>
                            <option value="">Pilih Bahagian</option>
                            <?php foreach ($senarai_bahagian as $bahagian): ?>
                                <option value="<?= $bahagian['id'] ?>"><?= htmlspecialchars($bahagian['bahagian']) ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-warning">Kemaskini</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Hapus Unit -->
<div class="modal fade" id="hapusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Hapus Unit</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Adakah anda pasti ingin menghapus unit <strong id="hapus_nama"></strong>?</p>
                <p class="text-danger"><i class="bi bi-exclamation-triangle me-2"></i>Tindakan ini tidak boleh dibatalkan.</p>
            </div>
            <div class="modal-footer">
                <form method="POST">
                    <input type="hidden" name="action" value="hapus">
                    <input type="hidden" name="id" id="hapus_id">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-danger">Hapus</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function editUnit(id, unit, idbahagian) {
    document.getElementById('edit_id').value = id;
    document.getElementById('edit_unit').value = unit;
    document.getElementById('edit_idbahagian').value = idbahagian;
    new bootstrap.Modal(document.getElementById('editModal')).show();
}

function hapusUnit(id, unit) {
    document.getElementById('hapus_id').value = id;
    document.getElementById('hapus_nama').textContent = unit;
    new bootstrap.Modal(document.getElementById('hapusModal')).show();
}
</script>

<?php require_once '../includes/footer_sistem.php'; ?>
