<?php
/**
 * Konfigurasi Utama Sistem
 * Sistem Tempahan Bilik Mesyuarat
 */

// <PERSON>lakan sesi jika belum dimulakan
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Tetapan masa dan zon waktu
date_default_timezone_set('Asia/Kuala_Lumpur');

// Tetapan sistem
define('SITE_NAME', 'Sistem Tempahan Bilik Mesyuarat');
define('SITE_URL', 'http://localhost/jkndata');
define('ADMIN_EMAIL', '<EMAIL>');

// Tetapan pangkalan data
require_once 'database.php';
define('DB_PASS', $_ENV['DB_PASS'] ?? '');

// Security settings
define('HASH_ALGO', PASSWORD_DEFAULT);
define('SESSION_TIMEOUT', 3600); // 1 hour

// Time settings
define('DEFAULT_TIMEZONE', 'Asia/Kuala_Lumpur');
date_default_timezone_set(DEFAULT_TIMEZONE);

// Business hours
define('BUSINESS_START_HOUR', 8);
define('BUSINESS_END_HOUR', 18);

// Booking settings
define('MIN_BOOKING_DURATION', 30); // minutes
define('MAX_BOOKING_DURATION', 480); // 8 hours
define('ADVANCE_BOOKING_DAYS', 30); // how many days in advance can book

// File upload settings
define('UPLOAD_MAX_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif']);

// Error reporting
if ($_ENV['APP_ENV'] ?? 'development' === 'development') {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// Include database configuration
require_once __DIR__ . '/database.php';

// Helper functions
function redirect($url) {
    header("Location: $url");
    exit();
}

function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

function isAdmin() {
    return isset($_SESSION['role']) && $_SESSION['role'] === 'admin';
}

function requireLogin() {
    if (!isLoggedIn()) {
        redirect('login.php');
    }
}

function requireAdmin() {
    requireLogin();
    if (!isAdmin()) {
        redirect('index.php');
    }
}

function formatCurrency($amount) {
    return 'RM ' . number_format($amount, 2);
}

function formatDateTime($datetime) {
    return date('d/m/Y H:i', strtotime($datetime));
}

function formatDate($date) {
    return date('d/m/Y', strtotime($date));
}

function formatTime($time) {
    return date('H:i', strtotime($time));
}

function sanitizeInput($input) {
    return htmlspecialchars(strip_tags(trim($input)));
}

function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

function validateCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}
?>
