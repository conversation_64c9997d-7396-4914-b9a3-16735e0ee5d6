<?php
/**
 * Halaman Senarai Tempahan untuk Penyelaras Bilik
 * Sistem Tempahan Bilik Mesyuarat
 */

session_start();

// Semak login dan profile
if (!isset($_SESSION['pengguna_id']) || $_SESSION['profile_id'] != 3) {
    header('Location: ../Logdaftar/log_masuk.php');
    exit;
}

// Konfigurasi database
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'sistem_tempahan_bilik';

$mesej_ralat = '';
$mesej_kejayaan = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Proses kelulusan/penolakan
    if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['tindakan'])) {
        $tempahan_id = (int)$_POST['tempahan_id'];
        $tindakan = $_POST['tindakan']; // 'LULUS' atau 'TOLAK'
        $ulasan = trim($_POST['ulasan'] ?? '');
        
        // Semak jika pengguna adalah penyelaras untuk bilik ini
        $sql_check = "SELECT t.*, b.nama_bilik_mesyuarat, bp.idpenyelaras
                      FROM ttempahan t
                      JOIN tbilik_mesyuarat b ON t.idbilik_mesyuarat = b.id
                      JOIN tbilik_penyelaras bp ON b.id = bp.idbilik_mesyuarat
                      WHERE t.id = ? AND bp.idpenyelaras = ? AND t.kelulusan = 'MENUNGGU'";
        
        $stmt = $pdo->prepare($sql_check);
        $stmt->execute([$tempahan_id, $_SESSION['pengguna_id']]);
        $tempahan = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$tempahan) {
            $mesej_ralat = 'Anda tidak mempunyai kebenaran untuk memproses tempahan ini.';
        } else {
            $status_baru = ($tindakan == 'LULUS') ? 'LULUS' : 'TOLAK';
            
            // Kemaskini status tempahan
            $sql_update = "UPDATE ttempahan SET 
                          kelulusan = ?, 
                          ulasan = ?, 
                          idpegawai_pelulus = ?, 
                          tarikh_lulus = NOW(),
                          tarikh_kemaskini = NOW(),
                          idpegawai_kemaskini = ?
                          WHERE id = ?";
            
            $stmt = $pdo->prepare($sql_update);
            $result = $stmt->execute([$status_baru, $ulasan, $_SESSION['pengguna_id'], $_SESSION['pengguna_id'], $tempahan_id]);
            
            if ($result) {
                $mesej_kejayaan = "Tempahan '{$tempahan['tajuk_mesyuarat']}' berjaya " . strtolower($status_baru) . ".";
            } else {
                $mesej_ralat = 'Ralat semasa mengemaskini status tempahan.';
            }
        }
    }
    
    // Dapatkan senarai bilik yang diuruskan oleh penyelaras ini
    $sql_bilik = "SELECT DISTINCT b.id, b.nama_bilik_mesyuarat
                  FROM tbilik_mesyuarat b
                  JOIN tbilik_penyelaras bp ON b.id = bp.idbilik_mesyuarat
                  WHERE bp.idpenyelaras = ?
                  ORDER BY b.nama_bilik_mesyuarat";
    
    $stmt = $pdo->prepare($sql_bilik);
    $stmt->execute([$_SESSION['pengguna_id']]);
    $bilik_diuruskan = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Dapatkan senarai tempahan untuk bilik yang diuruskan
    $sql_tempahan = "SELECT t.*, p.nama_penuh as nama_pemohon, p.emel as emel_pemohon,
                            b.nama_bilik_mesyuarat, b.kapasiti,
                            pelulus.nama_penuh as nama_pelulus
                     FROM ttempahan t
                     JOIN pengguna p ON t.idpemohon = p.id
                     JOIN tbilik_mesyuarat b ON t.idbilik_mesyuarat = b.id
                     JOIN tbilik_penyelaras bp ON b.id = bp.idbilik_mesyuarat
                     LEFT JOIN pengguna pelulus ON t.idpegawai_pelulus = pelulus.id
                     WHERE bp.idpenyelaras = ? AND t.batal_tempahan != 'BATAL'
                     ORDER BY 
                        CASE t.kelulusan 
                            WHEN 'MENUNGGU' THEN 1 
                            WHEN 'LULUS' THEN 2 
                            WHEN 'TOLAK' THEN 3 
                        END,
                        t.tarikh_mula DESC";
    
    $stmt = $pdo->prepare($sql_tempahan);
    $stmt->execute([$_SESSION['pengguna_id']]);
    $senarai_tempahan = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Statistik
    $sql_stats = "SELECT 
                    COUNT(*) as jumlah_tempahan,
                    COUNT(CASE WHEN t.kelulusan = 'MENUNGGU' THEN 1 END) as menunggu,
                    COUNT(CASE WHEN t.kelulusan = 'LULUS' THEN 1 END) as diluluskan,
                    COUNT(CASE WHEN t.kelulusan = 'TOLAK' THEN 1 END) as ditolak
                  FROM ttempahan t
                  JOIN tbilik_mesyuarat b ON t.idbilik_mesyuarat = b.id
                  JOIN tbilik_penyelaras bp ON b.id = bp.idbilik_mesyuarat
                  WHERE bp.idpenyelaras = ? AND t.batal_tempahan != 'BATAL'";
    
    $stmt = $pdo->prepare($sql_stats);
    $stmt->execute([$_SESSION['pengguna_id']]);
    $statistik = $stmt->fetch(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $mesej_ralat = 'Ralat sistem: ' . $e->getMessage();
    $senarai_tempahan = [];
    $bilik_diuruskan = [];
    $statistik = ['jumlah_tempahan' => 0, 'menunggu' => 0, 'diluluskan' => 0, 'ditolak' => 0];
}

function formatTarikhMasa($datetime) {
    return date('d/m/Y H:i', strtotime($datetime));
}

function getBadgeClass($status) {
    switch ($status) {
        case 'MENUNGGU': return 'bg-warning text-dark';
        case 'LULUS': return 'bg-success';
        case 'TOLAK': return 'bg-danger';
        default: return 'bg-secondary';
    }
}
?>

<!DOCTYPE html>
<html lang="ms">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Penyelaras Bilik - Sistem Tempahan Bilik Mesyuarat</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body class="bg-light">
    <!-- Header -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="bi bi-building me-2"></i>Sistem Tempahan Bilik
            </a>
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="bi bi-person-circle me-1"></i><?= htmlspecialchars($_SESSION['nama_penuh']) ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><span class="dropdown-header">
                            <strong><?= htmlspecialchars($_SESSION['nama_penuh']) ?></strong><br>
                            <small><?= htmlspecialchars($_SESSION['profile_name']) ?></small>
                        </span></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="log_keluar.php">
                            <i class="bi bi-box-arrow-right me-2"></i>Log Keluar
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Header Halaman -->
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h1 class="h3 mb-0">Dashboard Penyelaras Bilik</h1>
                        <p class="text-muted mb-0">Kelola tempahan bilik mesyuarat yang anda uruskan</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mesej -->
        <?php if (!empty($mesej_ralat)): ?>
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle me-2"></i><?= $mesej_ralat ?>
            </div>
        <?php endif; ?>

        <?php if (!empty($mesej_kejayaan)): ?>
            <div class="alert alert-success">
                <i class="bi bi-check-circle me-2"></i><?= $mesej_kejayaan ?>
            </div>
        <?php endif; ?>

        <!-- Statistik -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="bi bi-list-check fs-1 text-primary mb-2"></i>
                        <h3 class="mb-0"><?= $statistik['jumlah_tempahan'] ?></h3>
                        <p class="text-muted mb-0">Jumlah Tempahan</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="bi bi-clock-history fs-1 text-warning mb-2"></i>
                        <h3 class="mb-0"><?= $statistik['menunggu'] ?></h3>
                        <p class="text-muted mb-0">Menunggu</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="bi bi-check-circle fs-1 text-success mb-2"></i>
                        <h3 class="mb-0"><?= $statistik['diluluskan'] ?></h3>
                        <p class="text-muted mb-0">Diluluskan</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="bi bi-x-circle fs-1 text-danger mb-2"></i>
                        <h3 class="mb-0"><?= $statistik['ditolak'] ?></h3>
                        <p class="text-muted mb-0">Ditolak</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bilik Yang Diuruskan -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-building me-2"></i>Bilik Yang Anda Uruskan</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <?php foreach ($bilik_diuruskan as $bilik): ?>
                        <div class="col-md-4 mb-2">
                            <span class="badge bg-primary"><?= htmlspecialchars($bilik['nama_bilik_mesyuarat']) ?></span>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>

        <!-- Senarai Tempahan -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-calendar-check me-2"></i>Senarai Tempahan</h5>
            </div>
            <div class="card-body">
                <?php if (empty($senarai_tempahan)): ?>
                    <div class="text-center py-5">
                        <i class="bi bi-calendar-x fs-1 text-muted mb-3"></i>
                        <h4 class="text-muted">Tiada tempahan</h4>
                        <p class="text-muted">Belum ada tempahan untuk bilik yang anda uruskan.</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Tajuk Mesyuarat</th>
                                    <th>Pemohon</th>
                                    <th>Bilik</th>
                                    <th>Tarikh & Masa</th>
                                    <th>Peserta</th>
                                    <th>Status</th>
                                    <th>Tindakan</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($senarai_tempahan as $tempahan): ?>
                                    <tr>
                                        <td>
                                            <strong><?= htmlspecialchars($tempahan['tajuk_mesyuarat']) ?></strong><br>
                                            <small class="text-muted">
                                                Pengerusi: <?= htmlspecialchars($tempahan['pengerusi']) ?>
                                            </small>
                                        </td>
                                        <td>
                                            <strong><?= htmlspecialchars($tempahan['nama_pemohon']) ?></strong><br>
                                            <small class="text-muted"><?= htmlspecialchars($tempahan['emel_pemohon']) ?></small>
                                        </td>
                                        <td>
                                            <?= htmlspecialchars($tempahan['nama_bilik_mesyuarat']) ?><br>
                                            <small class="text-muted">Kapasiti: <?= $tempahan['kapasiti'] ?> orang</small>
                                        </td>
                                        <td>
                                            <strong><?= formatTarikhMasa($tempahan['tarikh_mula']) ?></strong><br>
                                            <small class="text-muted">hingga <?= formatTarikhMasa($tempahan['tarikh_tamat']) ?></small>
                                        </td>
                                        <td class="text-center">
                                            <span class="badge bg-info"><?= $tempahan['bilangan_peserta'] ?> orang</span>
                                        </td>
                                        <td>
                                            <span class="badge <?= getBadgeClass($tempahan['kelulusan']) ?>">
                                                <?= $tempahan['kelulusan'] ?>
                                            </span>
                                            <?php if ($tempahan['kelulusan'] != 'MENUNGGU' && $tempahan['nama_pelulus']): ?>
                                                <br><small class="text-muted">oleh <?= htmlspecialchars($tempahan['nama_pelulus']) ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($tempahan['kelulusan'] == 'MENUNGGU'): ?>
                                                <button type="button" class="btn btn-success btn-sm me-1" 
                                                        onclick="prosesKelulusan(<?= $tempahan['id'] ?>, 'LULUS', '<?= htmlspecialchars($tempahan['tajuk_mesyuarat']) ?>')">
                                                    <i class="bi bi-check-lg"></i>
                                                </button>
                                                <button type="button" class="btn btn-danger btn-sm" 
                                                        onclick="prosesKelulusan(<?= $tempahan['id'] ?>, 'TOLAK', '<?= htmlspecialchars($tempahan['tajuk_mesyuarat']) ?>')">
                                                    <i class="bi bi-x-lg"></i>
                                                </button>
                                            <?php else: ?>
                                                <small class="text-muted">Selesai</small>
                                                <?php if (!empty($tempahan['ulasan'])): ?>
                                                    <br><small class="text-muted">Ulasan: <?= htmlspecialchars($tempahan['ulasan']) ?></small>
                                                <?php endif; ?>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Modal Kelulusan -->
    <div class="modal fade" id="modalKelulusan" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="POST" action="">
                    <div class="modal-header">
                        <h5 class="modal-title" id="modalTitle">Proses Tempahan</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <input type="hidden" name="tempahan_id" id="tempahan_id">
                        <input type="hidden" name="tindakan" id="tindakan">
                        
                        <div class="mb-3">
                            <label for="ulasan" class="form-label">Ulasan (Pilihan)</label>
                            <textarea class="form-control" id="ulasan" name="ulasan" rows="3" 
                                      placeholder="Masukkan ulasan jika perlu..."></textarea>
                        </div>
                        
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                            <span id="pesanTindakan"></span>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                        <button type="submit" class="btn" id="btnSubmit">Sahkan</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function prosesKelulusan(tempahan_id, tindakan, tajuk) {
            document.getElementById('tempahan_id').value = tempahan_id;
            document.getElementById('tindakan').value = tindakan;
            
            const modal = document.getElementById('modalKelulusan');
            const title = document.getElementById('modalTitle');
            const pesan = document.getElementById('pesanTindakan');
            const btnSubmit = document.getElementById('btnSubmit');
            
            if (tindakan === 'LULUS') {
                title.textContent = 'Luluskan Tempahan';
                pesan.textContent = 'Anda akan meluluskan tempahan "' + tajuk + '".';
                btnSubmit.textContent = 'Luluskan';
                btnSubmit.className = 'btn btn-success';
            } else {
                title.textContent = 'Tolak Tempahan';
                pesan.textContent = 'Anda akan menolak tempahan "' + tajuk + '". Sila berikan sebab penolakan.';
                btnSubmit.textContent = 'Tolak';
                btnSubmit.className = 'btn btn-danger';
            }
            
            new bootstrap.Modal(modal).show();
        }
    </script>
</body>
</html>
