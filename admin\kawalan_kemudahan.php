<?php
/**
 * <PERSON><PERSON> Kawalan Kemudahan Bilik Mesyuarat
 * Sistem Tempahan Bilik Mesyuarat
 */

$tajuk_halaman = 'Kawalan <PERSON>';
require_once '../config/sistem_config.php';

// Semak login dan peranan admin
perluLogin();
if (!isset($_SESSION['profile_id']) || $_SESSION['profile_id'] != 2 && $_SESSION['profile_id'] != 3) {
    header('Location: ../Menuutama/dashboard.php');
    exit;
}

$mesej_kejayaan = '';
$mesej_ralat = '';

// Fungsi untuk mendapatkan semua kemudahan dari tkemudahan
function getAllKemudahan($db) {
    $kemudahan_list = [];

    try {
        // Dapatkan dari table tkemudahan
        $kemudahan_data = $db->fetchAll("SELECT kemudahan FROM tkemudahan ORDER BY kemudahan");
        $kemudahan_list = array_column($kemudahan_data, 'kemudahan');
    } catch (Exception $e) {
        // Jika table tidak wujud, kembalikan senarai default
        $kemudahan_list = [
            'Projektor',
            'Papan Putih',
            'WiFi',
            'Penyaman Udara',
            'Sistem Audio',
            'Video Conference'
        ];
    }

    return $kemudahan_list;
}

// Fungsi untuk menghitung penggunaan kemudahan
function getKemudahanUsage($db, $kemudahan) {
    $count = 0;
    $bilik_names = [];

    try {
        // Dapatkan dari table tbilik_kemudahan
        $bilik_list = $db->fetchAll("
            SELECT b.nama_bilik_mesyuarat 
            FROM tbilik_kemudahan bk
            JOIN tkemudahan k ON bk.idkemudahan = k.id
            JOIN tbilik_mesyuarat b ON bk.idbilik_mesyuarat = b.id
            WHERE k.kemudahan = ?
        ", [$kemudahan]);
        
        $count = count($bilik_list);
        $bilik_names = array_column($bilik_list, 'nama_bilik_mesyuarat');
    } catch (Exception $e) {
        $count = 0;
        $bilik_names = [];
    }

    return ['count' => $count, 'bilik_names' => $bilik_names];
}

// Proses form
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        $action = $_POST['action'];
        
        if ($action == 'tambah') {
            $kemudahan_baru = trim($_POST['kemudahan_baru']);
            
            if (empty($kemudahan_baru)) {
                $mesej_ralat = 'Nama kemudahan diperlukan.';
            } else {
                try {
                    // Semak jika kemudahan sudah wujud
                    $existing = $db->fetch("SELECT id FROM tkemudahan WHERE kemudahan = ?", [$kemudahan_baru]);
                    if ($existing) {
                        $mesej_ralat = 'Kemudahan sudah wujud dalam sistem.';
                    } else {
                        $sql = "INSERT INTO tkemudahan (kemudahan) VALUES (?)";
                        $db->query($sql, [$kemudahan_baru]);
                        $mesej_kejayaan = "Kemudahan '$kemudahan_baru' berjaya ditambah ke dalam sistem.";
                    }
                } catch (Exception $e) {
                    $mesej_ralat = 'Ralat: ' . $e->getMessage();
                }
            }
        } elseif ($action == 'edit') {
            $kemudahan_lama = trim($_POST['kemudahan_lama']);
            $kemudahan_baru = trim($_POST['kemudahan_baru']);
            
            if (empty($kemudahan_baru)) {
                $mesej_ralat = 'Nama kemudahan baru diperlukan.';
            } else {
                try {
                    // Kemaskini kemudahan dalam table tkemudahan
                    $sql = "UPDATE tkemudahan SET kemudahan = ? WHERE kemudahan = ?";
                    $result = $db->query($sql, [$kemudahan_baru, $kemudahan_lama]);
                    
                    if ($result) {
                        $mesej_kejayaan = "Kemudahan '$kemudahan_lama' berjaya dikemaskini kepada '$kemudahan_baru'.";
                    } else {
                        $mesej_ralat = 'Kemudahan tidak dijumpai atau tidak dikemaskini.';
                    }
                } catch (Exception $e) {
                    $mesej_ralat = 'Ralat: ' . $e->getMessage();
                }
            }
        } elseif ($action == 'hapus') {
            $kemudahan_hapus = trim($_POST['kemudahan_hapus']);
            
            try {
                // Semak jika kemudahan digunakan oleh bilik
                $usage = getKemudahanUsage($db, $kemudahan_hapus);
                
                if ($usage['count'] > 0) {
                    $mesej_ralat = "Kemudahan '$kemudahan_hapus' tidak boleh dihapus kerana masih digunakan oleh {$usage['count']} bilik.";
                } else {
                    // Hapus kemudahan dari table tkemudahan
                    $sql = "DELETE FROM tkemudahan WHERE kemudahan = ?";
                    $result = $db->query($sql, [$kemudahan_hapus]);
                    
                    if ($result) {
                        $mesej_kejayaan = "Kemudahan '$kemudahan_hapus' berjaya dihapus.";
                    } else {
                        $mesej_ralat = 'Kemudahan tidak dijumpai.';
                    }
                }
            } catch (Exception $e) {
                $mesej_ralat = 'Ralat: ' . $e->getMessage();
            }
        }
    }
}

// Dapatkan senarai kemudahan dengan statistik penggunaan
$senarai_kemudahan = getAllKemudahan($db);
$kemudahan_stats = [];
foreach ($senarai_kemudahan as $kemudahan) {
    $kemudahan_stats[$kemudahan] = getKemudahanUsage($db, $kemudahan);
}

require_once '../includes/header_sistem.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="bi bi-tools me-2"></i>Kawalan Kemudahan Bilik Mesyuarat</h2>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="../Menuutama/dashboard.php">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="kawalan.php">Kawalan</a></li>
                        <li class="breadcrumb-item active">Kemudahan</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Mesej -->
    <?php if ($mesej_kejayaan): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="bi bi-check-circle me-2"></i><?= htmlspecialchars($mesej_kejayaan) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($mesej_ralat): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="bi bi-exclamation-triangle me-2"></i><?= htmlspecialchars($mesej_ralat) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- Form Tambah Kemudahan -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-plus-circle me-2"></i>Tambah Kemudahan Baru</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <input type="hidden" name="action" value="tambah">
                        
                        <div class="mb-3">
                            <label for="kemudahan_baru" class="form-label">Nama Kemudahan</label>
                            <input type="text" class="form-control" id="kemudahan_baru" name="kemudahan_baru" required>
                            <div class="form-text">Contoh: Projektor, Papan Putih, WiFi, dll.</div>
                        </div>
                        
                        <button type="submit" class="btn btn-warning">
                            <i class="bi bi-plus-circle me-2"></i>Tambah Kemudahan
                        </button>
                    </form>
                    
                    <hr>
                    
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        <strong>Nota:</strong> Kemudahan yang ditambah di sini akan tersedia untuk dipilih semasa menguruskan bilik mesyuarat.
                    </div>
                </div>
            </div>
        </div>

        <!-- Senarai Kemudahan -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-list me-2"></i>Senarai Kemudahan Tersedia</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($senarai_kemudahan)): ?>
                        <div class="text-center py-4">
                            <i class="bi bi-inbox fs-1 text-muted"></i>
                            <p class="text-muted mt-2">Tiada kemudahan dijumpai.</p>
                            <p class="text-muted">Tambah kemudahan baru atau cipta bilik mesyuarat dengan kemudahan.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Nama Kemudahan</th>
                                        <th>Digunakan Oleh</th>
                                        <th>Bilik</th>
                                        <th>Tindakan</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($senarai_kemudahan as $kemudahan): ?>
                                        <tr>
                                            <td>
                                                <i class="bi bi-gear me-2"></i>
                                                <?= htmlspecialchars($kemudahan) ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary"><?= $kemudahan_stats[$kemudahan]['count'] ?> bilik</span>
                                            </td>
                                            <td>
                                                <?php if (!empty($kemudahan_stats[$kemudahan]['bilik_names'])): ?>
                                                    <small class="text-muted">
                                                        <?= htmlspecialchars(implode(', ', array_slice($kemudahan_stats[$kemudahan]['bilik_names'], 0, 3))) ?>
                                                        <?php if (count($kemudahan_stats[$kemudahan]['bilik_names']) > 3): ?>
                                                            <span class="badge bg-secondary">+<?= count($kemudahan_stats[$kemudahan]['bilik_names']) - 3 ?></span>
                                                        <?php endif; ?>
                                                    </small>
                                                <?php else: ?>
                                                    <small class="text-muted">Tiada bilik</small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <button class="btn btn-sm btn-warning me-1" 
                                                        onclick="editKemudahan('<?= htmlspecialchars($kemudahan) ?>')">
                                                    <i class="bi bi-pencil"></i>
                                                </button>
                                                <button class="btn btn-sm btn-danger" 
                                                        onclick="hapusKemudahan('<?= htmlspecialchars($kemudahan) ?>', <?= $kemudahan_stats[$kemudahan]['count'] ?>)">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Edit Kemudahan -->
<div class="modal fade" id="editModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Kemudahan</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="edit">
                    <input type="hidden" name="kemudahan_lama" id="edit_kemudahan_lama">
                    
                    <div class="mb-3">
                        <label for="edit_kemudahan_baru" class="form-label">Nama Kemudahan Baru</label>
                        <input type="text" class="form-control" id="edit_kemudahan_baru" name="kemudahan_baru" required>
                    </div>
                    
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        Perubahan ini akan mempengaruhi semua bilik yang menggunakan kemudahan ini.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-warning">Kemaskini</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Hapus Kemudahan -->
<div class="modal fade" id="hapusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Hapus Kemudahan</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Adakah anda pasti ingin menghapus kemudahan <strong id="hapus_nama"></strong>?</p>
                <p class="text-danger">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    Kemudahan ini akan dihapus dari <strong id="hapus_count"></strong> bilik mesyuarat. Tindakan ini tidak boleh dibatalkan.
                </p>
            </div>
            <div class="modal-footer">
                <form method="POST">
                    <input type="hidden" name="action" value="hapus">
                    <input type="hidden" name="kemudahan_hapus" id="hapus_kemudahan">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-danger">Hapus</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function editKemudahan(kemudahan) {
    document.getElementById('edit_kemudahan_lama').value = kemudahan;
    document.getElementById('edit_kemudahan_baru').value = kemudahan;
    new bootstrap.Modal(document.getElementById('editModal')).show();
}

function hapusKemudahan(kemudahan, count) {
    document.getElementById('hapus_kemudahan').value = kemudahan;
    document.getElementById('hapus_nama').textContent = kemudahan;
    document.getElementById('hapus_count').textContent = count;
    new bootstrap.Modal(document.getElementById('hapusModal')).show();
}
</script>

<?php require_once '../includes/footer_sistem.php'; ?>



