<?php
/**
 * Halaman Tambah Bilik Mesyuarat
 * Sistem Tempahan Bilik Mesyuarat
 */

$tajuk_halaman = 'Tambah Bilik Mesyuarat';
require_once '../config/sistem_config.php';

// Semak login dan peranan admin
perluLogin();
if (!isset($_SESSION['profile_id']) || $_SESSION['profile_id'] != 2) {
    header('Location: senarai_bilik.php');
    exit;
}

$mesej_kejayaan = '';
$mesej_ralat = '';

// Proses form tambah bilik
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['tambah_bilik'])) {
    $nama_bilik = trim($_POST['nama_bilik']);
    $kapasiti = (int)$_POST['kapasiti'];
    $bahagian = (int)$_POST['bahagian'];
    
    // Validasi input
    if (empty($nama_bilik) || empty($kapasiti) || empty($bahagian)) {
        $mesej_ralat = 'Nama bilik, kapasiti dan bahagian adalah wajib.';
    } else {
        try {
            // Generate ID automatically or get next available ID
            $next_id = $db->fetch("SELECT COALESCE(MAX(id), 0) + 1 as next_id FROM tbilik_mesyuarat");
            $id = $next_id['next_id'];
            
            // Masukkan bilik baru (tanpa kemudahan)
            $sql = "INSERT INTO tbilik_mesyuarat (id, nama_bilik_mesyuarat, kapasiti, bahagian) 
                    VALUES (?, ?, ?, ?)";
            
            $result = $db->query($sql, [$id, $nama_bilik, $kapasiti, $bahagian]);
            
            if ($result) {
                // Tambah kemudahan ke tbilik_kemudahan jika ada
                if (isset($_POST['kemudahan']) && is_array($_POST['kemudahan'])) {
                    foreach ($_POST['kemudahan'] as $kemudahan_nama) {
                        // Cari atau cipta kemudahan
                        $kemudahan_id = $db->fetch("SELECT id FROM tkemudahan WHERE kemudahan = ?", [$kemudahan_nama]);
                        if (!$kemudahan_id) {
                            // Cipta kemudahan baru
                            $new_kemudahan_id = $db->fetch("SELECT COALESCE(MAX(id), 0) + 1 as next_id FROM tkemudahan");
                            $db->query("INSERT INTO tkemudahan (id, kemudahan) VALUES (?, ?)", 
                                     [$new_kemudahan_id['next_id'], $kemudahan_nama]);
                            $kemudahan_id = ['id' => $new_kemudahan_id['next_id']];
                        }
                        
                        // Tambah ke tbilik_kemudahan
                        $bilik_kemudahan_id = $db->fetch("SELECT COALESCE(MAX(id), 0) + 1 as next_id FROM tbilik_kemudahan");
                        $db->query("INSERT INTO tbilik_kemudahan (id, idbilik_mesyuarat, idkemudahan, bilangan) VALUES (?, ?, ?, 1)", 
                                 [$bilik_kemudahan_id['next_id'], $id, $kemudahan_id['id']]);
                    }
                }
                
                $mesej_kejayaan = 'Bilik mesyuarat berjaya ditambah.';
                $_POST = [];
            } else {
                $mesej_ralat = 'Ralat semasa menambah bilik mesyuarat.';
            }
        } catch (Exception $e) {
            $mesej_ralat = 'Ralat: ' . $e->getMessage();
        }
    }
}

// Dapatkan senarai bahagian
try {
    $senarai_bahagian = $db->fetchAll("SELECT * FROM tbahagian ORDER BY bahagian");
} catch (Exception $e) {
    $senarai_bahagian = [];
}

// Dapatkan senarai kemudahan dari database
try {
    $kemudahan_tersedia = $db->fetchAll("SELECT kemudahan FROM tkemudahan ORDER BY kemudahan");
    $kemudahan_tersedia = array_column($kemudahan_tersedia, 'kemudahan');
} catch (Exception $e) {
    // Fallback ke senarai default jika table tidak wujud
    $kemudahan_tersedia = [
        'Projektor', 'Papan Putih', 'WiFi', 'Penyaman Udara', 'Sistem Audio',
        'Video Conference', 'Flip Chart', 'Komputer', 'Printer', 'Telefon'
    ];
}

require_once '../includes/header_sistem.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="bi bi-plus-circle me-2"></i>Tambah Bilik Mesyuarat</h2>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="dashboard.php">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="senarai_bilik.php">Senarai Bilik</a></li>
                        <li class="breadcrumb-item active">Tambah Bilik</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Mesej -->
    <?php if ($mesej_kejayaan): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="bi bi-check-circle me-2"></i><?= htmlspecialchars($mesej_kejayaan) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($mesej_ralat): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="bi bi-exclamation-triangle me-2"></i><?= htmlspecialchars($mesej_ralat) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-door-open me-2"></i>Maklumat Bilik Mesyuarat</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="nama_bilik" class="form-label">Nama Bilik *</label>
                                <input type="text" class="form-control" id="nama_bilik" name="nama_bilik" 
                                       value="<?= htmlspecialchars($_POST['nama_bilik'] ?? '') ?>" required>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="kapasiti" class="form-label">Kapasiti *</label>
                                <input type="number" class="form-control" id="kapasiti" name="kapasiti" 
                                       value="<?= htmlspecialchars($_POST['kapasiti'] ?? '') ?>" required min="1">
                                <div class="form-text">Bilangan orang maksimum</div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="bahagian" class="form-label">Bahagian *</label>
                                <select class="form-select" id="bahagian" name="bahagian" required>
                                    <option value="">Pilih Bahagian</option>
                                    <?php foreach ($senarai_bahagian as $bahagian): ?>
                                        <option value="<?= $bahagian['id'] ?>" 
                                                <?= (($_POST['bahagian'] ?? '') == $bahagian['id']) ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($bahagian['bahagian']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Kemudahan Tersedia</label>
                            <div class="row">
                                <?php foreach ($kemudahan_tersedia as $kemudahan): ?>
                                    <div class="col-md-4 col-sm-6 mb-2">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" 
                                                   id="kemudahan_<?= md5($kemudahan) ?>" 
                                                   name="kemudahan[]" value="<?= htmlspecialchars($kemudahan) ?>"
                                                   <?= (isset($_POST['kemudahan']) && in_array($kemudahan, $_POST['kemudahan'])) ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="kemudahan_<?= md5($kemudahan) ?>">
                                                <?= htmlspecialchars($kemudahan) ?>
                                            </label>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="senarai_bilik.php" class="btn btn-secondary me-md-2">
                                <i class="bi bi-arrow-left me-2"></i>Kembali
                            </a>
                            <button type="submit" name="tambah_bilik" class="btn btn-primary">
                                <i class="bi bi-plus-circle me-2"></i>Tambah Bilik
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Panel Bantuan -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6><i class="bi bi-info-circle me-2"></i>Panduan</h6>
                </div>
                <div class="card-body">
                    <h6>Medan Wajib:</h6>
                    <ul class="small">
                        <li><strong>Nama Bilik:</strong> Nama yang mudah dikenali</li>
                        <li><strong>Kapasiti:</strong> Bilangan orang maksimum</li>
                        <li><strong>Bahagian:</strong> Bahagian yang menguruskan bilik</li>
                    </ul>
                    
                    <h6 class="mt-3">Tips:</h6>
                    <ul class="small">
                        <li>Gunakan nama yang jelas dan mudah difahami</li>
                        <li>Pastikan kapasiti adalah realistik</li>
                        <li>Pilih kemudahan yang betul-betul tersedia</li>
                        <li>Lokasi dan tingkat membantu pengguna mencari bilik</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../includes/footer_sistem.php'; ?>





