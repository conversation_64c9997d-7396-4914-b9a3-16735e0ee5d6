<?php
/**
 * Tempah Bilik - Sistem Sebenar
 * Sistem Tempahan Bilik Mesyuarat
 */

session_start();

// Semak login
if (!isset($_SESSION['pengguna_id'])) {
    header('Location: ../Logdaftar/log_masuk.php');
    exit;
}

// Konfigurasi database
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'sistem_tempahan_bilik';

$mesej_ralat = '';
$mesej_kejayaan = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Proses tempahan
    if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['buat_tempahan'])) {
        $tajuk_mesyuarat = trim($_POST['tajuk_mesyuarat']);
        $idbilik_mesyuarat = (int)$_POST['idbilik_mesyuarat'];
        $tarikh_mula = $_POST['tarikh_mula'];
        $tarikh_tamat = $_POST['tarikh_tamat'];
        $bilangan_peserta = (int)$_POST['bilangan_peserta'];
        $pengerusi = trim($_POST['pengerusi']);
        $sesi = (int)$_POST['sesi'];
        
        // Validasi
        if (empty($tajuk_mesyuarat) || empty($tarikh_mula) || empty($tarikh_tamat) || empty($pengerusi)) {
            $mesej_ralat = 'Sila lengkapkan semua maklumat yang diperlukan.';
        } elseif (strtotime($tarikh_mula) >= strtotime($tarikh_tamat)) {
            $mesej_ralat = 'Masa tamat mestilah selepas masa mula.';
        } elseif (strtotime($tarikh_mula) < time()) {
            $mesej_ralat = 'Tarikh dan masa tempahan mestilah pada masa hadapan.';
        } else {
            // Semak konflik tempahan
            $sql_check = "SELECT COUNT(*) FROM ttempahan 
                         WHERE idbilik_mesyuarat = ? 
                         AND batal_tempahan != 'BATAL'
                         AND kelulusan != 'TOLAK'
                         AND (
                             (tarikh_mula <= ? AND tarikh_tamat > ?) OR
                             (tarikh_mula < ? AND tarikh_tamat >= ?) OR
                             (tarikh_mula >= ? AND tarikh_tamat <= ?)
                         )";
            
            $stmt = $pdo->prepare($sql_check);
            $stmt->execute([$idbilik_mesyuarat, $tarikh_mula, $tarikh_mula, $tarikh_tamat, $tarikh_tamat, $tarikh_mula, $tarikh_tamat]);
            $konflik = $stmt->fetchColumn();
            
            if ($konflik > 0) {
                $mesej_ralat = 'Bilik telah ditempah pada masa tersebut. Sila pilih masa lain.';
            } else {
                // Masukkan tempahan baru
                $sql_insert = "INSERT INTO ttempahan (
                    idpemohon, tarikh_mohon, tajuk_mesyuarat, idbilik_mesyuarat,
                    tarikh_mula, tarikh_tamat, sesi, bilangan_peserta, pengerusi,
                    tahun, kelulusan, batal_tempahan, tarikh_kemaskini
                ) VALUES (?, NOW(), ?, ?, ?, ?, ?, ?, ?, YEAR(NOW()), 'MENUNGGU', 'TIDAK', NOW())";
                
                $stmt = $pdo->prepare($sql_insert);
                $result = $stmt->execute([
                    $_SESSION['pengguna_id'], $tajuk_mesyuarat, $idbilik_mesyuarat,
                    $tarikh_mula, $tarikh_tamat, $sesi, $bilangan_peserta, $pengerusi
                ]);
                
                if ($result) {
                    $mesej_kejayaan = "Tempahan '$tajuk_mesyuarat' berjaya dihantar dan menunggu kelulusan penyelaras.";
                    
                    // Reset form
                    $_POST = [];
                } else {
                    $mesej_ralat = 'Ralat semasa menyimpan tempahan. Sila cuba lagi.';
                }
            }
        }
    }
    
    // Dapatkan senarai bilik
    $sql_bilik = "SELECT * FROM tbilik_mesyuarat WHERE status = 'tersedia' ORDER BY nama_bilik_mesyuarat";
    $stmt = $pdo->prepare($sql_bilik);
    $stmt->execute();
    $senarai_bilik = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Auto-pilih bilik jika datang dari senarai bilik atau kalendar
    $selected_bilik_id = $_GET['bilik_id'] ?? $_GET['bilik'] ?? $_POST['idbilik_mesyuarat'] ?? '';

    // Pre-fill data dari kalendar
    $prefill_tarikh_mula = $_GET['tarikh_mula'] ?? $_POST['tarikh_mula'] ?? '';
    $prefill_tarikh_tamat = $_GET['tarikh_tamat'] ?? $_POST['tarikh_tamat'] ?? '';
    $prefill_sesi = $_GET['sesi'] ?? $_POST['sesi'] ?? '';

    // Jika datang dari kalendar, set nilai default
    if (!empty($prefill_tarikh_mula) && !empty($prefill_tarikh_tamat)) {
        // Format untuk datetime-local input
        $default_tarikh_mula = $prefill_tarikh_mula;
        $default_tarikh_tamat = $prefill_tarikh_tamat;
        $default_sesi = $prefill_sesi;
    } else {
        $default_tarikh_mula = '';
        $default_tarikh_tamat = '';
        $default_sesi = '';
    }

} catch (Exception $e) {
    $mesej_ralat = 'Ralat sistem: ' . $e->getMessage();
    $senarai_bilik = [];
}
?>

<?php $tajuk_halaman = 'Tempah Bilik'; require_once '../includes/header_sistem.php'; ?>

    <div class="container mt-4">
        <!-- Header Halaman -->
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h1 class="h3 mb-0">Tempah Bilik Mesyuarat</h1>
                        <p class="text-muted mb-0">Buat tempahan bilik untuk mesyuarat atau acara anda</p>
                    </div>
                    <div>
                        <a href="../Menuutama/dashboard.php" class="btn btn-outline-primary">
                            <i class="bi bi-arrow-left me-2"></i>Kembali
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mesej -->
        <?php if (!empty($mesej_ralat)): ?>
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle me-2"></i><?= $mesej_ralat ?>
            </div>
        <?php endif; ?>

        <?php if (!empty($mesej_kejayaan)): ?>
            <div class="alert alert-success">
                <i class="bi bi-check-circle me-2"></i><?= $mesej_kejayaan ?>
            </div>
        <?php endif; ?>

        <div class="row">
            <!-- Borang Tempahan -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-calendar-plus me-2"></i>Borang Tempahan</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="">
                            <input type="hidden" name="buat_tempahan" value="1">

                            <!-- Maklumat Permohonan (Auto) -->
                            <div class="card mb-4 bg-light">
                                <div class="card-body">
                                    <h6 class="card-title"><i class="bi bi-calendar-date me-2"></i>Maklumat Permohonan</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <small class="text-muted">Tarikh Mohon:</small><br>
                                            <strong><?= date('d/m/Y') ?></strong>
                                        </div>
                                        <div class="col-md-6">
                                            <small class="text-muted">Masa Mohon:</small><br>
                                            <strong><?= date('H:i:s') ?></strong>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Maklumat Mesyuarat -->
                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <label for="tajuk_mesyuarat" class="form-label">
                                        <i class="bi bi-card-text me-2"></i>Tajuk Mesyuarat *
                                    </label>
                                    <input type="text" class="form-control" id="tajuk_mesyuarat" name="tajuk_mesyuarat"
                                           value="<?= htmlspecialchars($_POST['tajuk_mesyuarat'] ?? '') ?>"
                                           placeholder="Contoh: Mesyuarat Bulanan Bahagian" required>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="idbilik_mesyuarat" class="form-label">
                                        <i class="bi bi-building me-2"></i>Bilik Mesyuarat *
                                    </label>
                                    <select class="form-select" id="idbilik_mesyuarat" name="idbilik_mesyuarat" required>
                                        <option value="">Pilih Bilik</option>
                                        <?php foreach ($senarai_bilik as $bilik): ?>
                                            <option value="<?= $bilik['id'] ?>" 
                                                    <?= ($bilik['id'] == $selected_bilik_id) ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($bilik['nama_bilik_mesyuarat']) ?> 
                                                (<?= $bilik['kapasiti'] ?> orang) - <?= htmlspecialchars($bilik['lokasi']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="pengerusi" class="form-label">
                                        <i class="bi bi-person-badge me-2"></i>Pengerusi Mesyuarat *
                                    </label>
                                    <input type="text" class="form-control" id="pengerusi" name="pengerusi"
                                           value="<?= htmlspecialchars($_POST['pengerusi'] ?? '') ?>"
                                           placeholder="Nama pengerusi mesyuarat" required>
                                </div>
                            </div>
                            
                            <!-- Tarikh dan Masa Mesyuarat -->
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="tarikh_mula" class="form-label">
                                        <i class="bi bi-calendar-event me-2"></i>Tarikh & Masa Mula *
                                    </label>
                                    <input type="datetime-local" class="form-control" id="tarikh_mula" name="tarikh_mula"
                                           value="<?= $_POST['tarikh_mula'] ?? $default_tarikh_mula ?>"
                                           min="<?= date('Y-m-d\TH:i') ?>" required>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="tarikh_tamat" class="form-label">
                                        <i class="bi bi-calendar-check me-2"></i>Tarikh & Masa Tamat *
                                    </label>
                                    <input type="datetime-local" class="form-control" id="tarikh_tamat" name="tarikh_tamat"
                                           value="<?= $_POST['tarikh_tamat'] ?? $default_tarikh_tamat ?>"
                                           min="<?= date('Y-m-d\TH:i') ?>" required>
                                </div>
                            </div>

                            <!-- Sesi dan Peserta -->
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="sesi" class="form-label">
                                        <i class="bi bi-clock me-2"></i>Sesi *
                                    </label>
                                    <select class="form-select" id="sesi" name="sesi" required>
                                        <option value="">Pilih Sesi</option>
                                        <option value="1" <?= (($_POST['sesi'] ?? $default_sesi) == '1') ? 'selected' : '' ?>>Pagi (8:00 AM - 12:00 PM)</option>
                                        <option value="2" <?= (($_POST['sesi'] ?? $default_sesi) == '2') ? 'selected' : '' ?>>Petang (2:00 PM - 5:00 PM)</option>
                                        <option value="3" <?= (($_POST['sesi'] ?? $default_sesi) == '3') ? 'selected' : '' ?>>Sepanjang Hari (8:00 AM - 5:00 PM)</option>
                                    </select>
                                </div>

                                <div class="col-md-4 mb-3">
                                    <label for="bilangan_peserta" class="form-label">
                                        <i class="bi bi-people me-2"></i>Bilangan Peserta *
                                    </label>
                                    <input type="number" class="form-control" id="bilangan_peserta" name="bilangan_peserta"
                                           value="<?= $_POST['bilangan_peserta'] ?? '10' ?>"
                                           min="1" max="100" required>
                                    <div class="form-text">Maksimum bergantung pada kapasiti bilik</div>
                                </div>

                                <div class="col-md-4 mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-info-circle me-2"></i>Status Permohonan
                                    </label>
                                    <input type="text" class="form-control" value="Menunggu Kelulusan" readonly>
                                    <div class="form-text">Status akan dikemaskini selepas semakan</div>
                                </div>
                            </div>
                            
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle me-2"></i>
                                <strong>Nota:</strong> Tempahan anda akan dihantar kepada penyelaras bilik untuk kelulusan. 
                                Anda akan menerima makluman status tempahan melalui sistem.
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="bi bi-send me-2"></i>Hantar Tempahan
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            
            <!-- Maklumat Tambahan -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-info-circle me-2"></i>Panduan Tempahan</h5>
                    </div>
                    <div class="card-body">
                        <h6>Langkah-langkah:</h6>
                        <ol class="small">
                            <li>Lengkapkan borang tempahan</li>
                            <li>Hantar tempahan untuk kelulusan</li>
                            <li>Tunggu kelulusan dari penyelaras</li>
                            <li>Terima notifikasi status tempahan</li>
                        </ol>
                        
                        <h6 class="mt-3">Masa Operasi:</h6>
                        <ul class="small">
                            <li><strong>Pagi:</strong> 8:00 - 12:00</li>
                            <li><strong>Petang:</strong> 14:00 - 17:00</li>
                            <li><strong>Sehari Penuh:</strong> 8:00 - 17:00</li>
                        </ul>
                        
                        <h6 class="mt-3">Nota Penting:</h6>
                        <ul class="small">
                            <li>Tempahan mestilah dibuat sekurang-kurangnya 24 jam sebelum acara</li>
                            <li>Pastikan bilangan peserta tidak melebihi kapasiti bilik</li>
                            <li>Tempahan tertakluk kepada kelulusan penyelaras</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php require_once '../includes/footer_sistem.php'; ?>
    <script>
        // Auto set masa tamat 2 jam selepas masa mula
        document.getElementById('tarikh_mula').addEventListener('change', function() {
            const masaMula = new Date(this.value);
            if (masaMula) {
                const masaTamat = new Date(masaMula.getTime() + (2 * 60 * 60 * 1000)); // +2 jam
                document.getElementById('tarikh_tamat').value = masaTamat.toISOString().slice(0, 16);
            }
        });

        // Validasi bilangan peserta berdasarkan kapasiti bilik
        document.getElementById('idbilik_mesyuarat').addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            if (selectedOption.value) {
                const text = selectedOption.text;
                const kapasiti = text.match(/\((\d+) orang\)/);
                if (kapasiti) {
                    const maxKapasiti = parseInt(kapasiti[1]);
                    document.getElementById('bilangan_peserta').max = maxKapasiti;

                    // Peringatan jika peserta melebihi kapasiti
                    const pesertaInput = document.getElementById('bilangan_peserta');
                    pesertaInput.addEventListener('input', function() {
                        if (parseInt(this.value) > maxKapasiti) {
                            this.setCustomValidity(`Bilangan peserta tidak boleh melebihi ${maxKapasiti} orang`);
                        } else {
                            this.setCustomValidity('');
                        }
                    });
                }
            }
        });
    </script>


