<?php
/**
 * Halaman Edit Bilik Mesyuarat
 * Sistem Tempahan Bilik Mesyuarat
 */

$tajuk_halaman = 'Edit Bilik Mesyuarat';
require_once '../config/sistem_config.php';

// Semak login dan peranan admin
perluLogin();
if (!isset($_SESSION['profile_id']) || $_SESSION['profile_id'] != 2) {
    header('Location: senarai_bilik.php');
    exit;
}

$mesej_kejayaan = '';
$mesej_ralat = '';
$bilik_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$bilik_id) {
    header('Location: senarai_bilik.php');
    exit;
}

// Dapatkan data bilik
try {
    $bilik = $db->fetch("SELECT * FROM tbilik_mesyuarat WHERE id = ?", [$bilik_id]);
    if (!$bilik) {
        header('Location: senarai_bilik.php');
        exit;
    }
} catch (Exception $e) {
    header('Location: senarai_bilik.php');
    exit;
}

// Proses form edit bilik
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['edit_bilik'])) {
    $nama_bilik = trim($_POST['nama_bilik']);
    $kapasiti = (int)$_POST['kapasiti'];
    $bahagian = (int)$_POST['bahagian'];
    
    // Validasi input
    if (empty($nama_bilik) || empty($kapasiti) || empty($bahagian)) {
        $mesej_ralat = 'Nama bilik, kapasiti dan bahagian adalah wajib.';
    } else {
        try {
            // Kemaskini bilik
            $sql = "UPDATE tbilik_mesyuarat SET 
                    nama_bilik_mesyuarat = ?, kapasiti = ?, bahagian = ? 
                    WHERE id = ?";
            
            $result = $db->query($sql, [
                $nama_bilik, $kapasiti, $bahagian, $bilik_id
            ]);
            
            if ($result) {
                // Hapus kemudahan lama
                $db->query("DELETE FROM tbilik_kemudahan WHERE idbilik_mesyuarat = ?", [$bilik_id]);
                
                // Tambah kemudahan baru
                if (isset($_POST['kemudahan']) && is_array($_POST['kemudahan'])) {
                    foreach ($_POST['kemudahan'] as $kemudahan_nama) {
                        // Cari atau cipta kemudahan
                        $kemudahan_id = $db->fetch("SELECT id FROM tkemudahan WHERE kemudahan = ?", [$kemudahan_nama]);
                        if (!$kemudahan_id) {
                            // Cipta kemudahan baru
                            $new_kemudahan_id = $db->fetch("SELECT COALESCE(MAX(id), 0) + 1 as next_id FROM tkemudahan");
                            $db->query("INSERT INTO tkemudahan (id, kemudahan) VALUES (?, ?)", 
                                     [$new_kemudahan_id['next_id'], $kemudahan_nama]);
                            $kemudahan_id = ['id' => $new_kemudahan_id['next_id']];
                        }
                        
                        // Tambah ke tbilik_kemudahan
                        $bilik_kemudahan_id = $db->fetch("SELECT COALESCE(MAX(id), 0) + 1 as next_id FROM tbilik_kemudahan");
                        $db->query("INSERT INTO tbilik_kemudahan (id, idbilik_mesyuarat, idkemudahan, bilangan) VALUES (?, ?, ?, 1)", 
                                 [$bilik_kemudahan_id['next_id'], $bilik_id, $kemudahan_id['id']]);
                    }
                }
                
                // Log aktiviti
                logAktiviti($_SESSION['pengguna_id'], 'Edit Bilik', "Bilik '$nama_bilik' dikemaskini");
                
                $mesej_kejayaan = 'Bilik mesyuarat berjaya dikemaskini.';
                
                // Refresh data bilik
                $bilik = $db->fetch("SELECT * FROM tbilik_mesyuarat WHERE id = ?", [$bilik_id]);
            } else {
                $mesej_ralat = 'Ralat semasa mengemaskini bilik mesyuarat.';
            }
        } catch (Exception $e) {
            $mesej_ralat = 'Ralat: ' . $e->getMessage();
        }
    }
}

// Dapatkan senarai bahagian
try {
    $senarai_bahagian = $db->fetchAll("SELECT * FROM tbahagian ORDER BY bahagian");
} catch (Exception $e) {
    $senarai_bahagian = [];
}

// Parse kemudahan yang ada dari tbilik_kemudahan
$kemudahan_terpilih = [];
try {
    $kemudahan_bilik = $db->fetchAll("
        SELECT k.kemudahan 
        FROM tbilik_kemudahan bk 
        JOIN tkemudahan k ON bk.idkemudahan = k.id 
        WHERE bk.idbilik_mesyuarat = ?", [$bilik_id]);
    
    $kemudahan_terpilih = array_column($kemudahan_bilik, 'kemudahan');
} catch (Exception $e) {
    $kemudahan_terpilih = [];
}

// Dapatkan senarai kemudahan dari database
try {
    $kemudahan_tersedia = $db->fetchAll("SELECT kemudahan FROM tkemudahan ORDER BY kemudahan");
    $kemudahan_tersedia = array_column($kemudahan_tersedia, 'kemudahan');
} catch (Exception $e) {
    // Fallback ke senarai default jika table tidak wujud
    $kemudahan_tersedia = [
        'Projektor', 'Papan Putih', 'WiFi', 'Penyaman Udara', 'Sistem Audio',
        'Video Conference', 'Flip Chart', 'Komputer', 'Printer', 'Telefon'
    ];
}

require_once '../includes/header_sistem.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="bi bi-pencil me-2"></i>Edit Bilik Mesyuarat</h2>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="dashboard.php">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="senarai_bilik.php">Senarai Bilik</a></li>
                        <li class="breadcrumb-item active">Edit Bilik</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Mesej -->
    <?php if ($mesej_kejayaan): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="bi bi-check-circle me-2"></i><?= htmlspecialchars($mesej_kejayaan) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($mesej_ralat): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="bi bi-exclamation-triangle me-2"></i><?= htmlspecialchars($mesej_ralat) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-door-open me-2"></i>Maklumat Bilik: <?= htmlspecialchars($bilik['nama_bilik_mesyuarat']) ?></h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="id" class="form-label">ID Bilik</label>
                                <input type="number" class="form-control" id="id" value="<?= $bilik['id'] ?>" disabled>
                                <div class="form-text">ID tidak boleh diubah</div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="nama_bilik" class="form-label">Nama Bilik *</label>
                                <input type="text" class="form-control" id="nama_bilik" name="nama_bilik" 
                                       value="<?= htmlspecialchars($bilik['nama_bilik_mesyuarat']) ?>" required>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="kapasiti" class="form-label">Kapasiti *</label>
                                <input type="number" class="form-control" id="kapasiti" name="kapasiti" 
                                       value="<?= $bilik['kapasiti'] ?>" min="1" required>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="bahagian" class="form-label">Bahagian *</label>
                                <select class="form-select" id="bahagian" name="bahagian" required>
                                    <option value="">Pilih Bahagian</option>
                                    <?php foreach ($senarai_bahagian as $bahagian): ?>
                                        <option value="<?= $bahagian['id'] ?>" 
                                                <?= ($bilik['bahagian'] == $bahagian['id']) ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($bahagian['bahagian']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Kemudahan Tersedia</label>
                            <div class="row">
                                <?php foreach ($kemudahan_tersedia as $kemudahan): ?>
                                    <div class="col-md-4 col-sm-6 mb-2">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" 
                                                   id="kemudahan_<?= md5($kemudahan) ?>" 
                                                   name="kemudahan[]" value="<?= htmlspecialchars($kemudahan) ?>"
                                                   <?= in_array($kemudahan, $kemudahan_terpilih) ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="kemudahan_<?= md5($kemudahan) ?>">
                                                <?= htmlspecialchars($kemudahan) ?>
                                            </label>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="senarai_bilik.php" class="btn btn-secondary me-md-2">
                                <i class="bi bi-arrow-left me-2"></i>Kembali
                            </a>
                            <button type="submit" name="edit_bilik" class="btn btn-warning">
                                <i class="bi bi-pencil me-2"></i>Kemaskini Bilik
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Panel Info -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6><i class="bi bi-info-circle me-2"></i>Maklumat Bilik</h6>
                </div>
                <div class="card-body">
                    <table class="table table-sm">
                        <tr>
                            <td><strong>ID Bilik:</strong></td>
                            <td><?= $bilik['id'] ?></td>
                        </tr>
                        <tr>
                            <td><strong>Dicipta:</strong></td>
                            <td><?= isset($bilik['created_at']) ? date('d/m/Y H:i', strtotime($bilik['created_at'])) : 'Tidak diketahui' ?></td>
                        </tr>
                        <tr>
                            <td><strong>Status:</strong></td>
                            <td>
                                <?php
                                $status_class = [
                                    'tersedia' => 'success',
                                    'tidak_tersedia' => 'danger',
                                    'penyelenggaraan' => 'warning'
                                ];
                                $status_text = [
                                    'tersedia' => 'Tersedia',
                                    'tidak_tersedia' => 'Tidak Tersedia',
                                    'penyelenggaraan' => 'Penyelenggaraan'
                                ];
                                ?>
                                <span class="badge bg-<?= $status_class[$bilik['status']] ?? 'secondary' ?>">
                                    <?= $status_text[$bilik['status']] ?? $bilik['status'] ?>
                                </span>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header">
                    <h6><i class="bi bi-exclamation-triangle me-2"></i>Amaran</h6>
                </div>
                <div class="card-body">
                    <p class="small text-muted">
                        Perubahan pada bilik ini akan mempengaruhi semua tempahan yang akan datang. 
                        Pastikan maklumat yang dimasukkan adalah tepat.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../includes/footer_sistem.php'; ?>




