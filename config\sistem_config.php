<?php
/**
 * Konfigurasi Utama Sistem
 * Sistem Tempahan Bilik Mesyuarat
 */

// Mulakan sesi jika belum dimulakan
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Tetapan masa dan zon waktu
date_default_timezone_set('Asia/Kuala_Lumpur');

// Tetapan sistem
define('NAMA_SISTEM', 'Sistem Tempahan Bilik Mesyuarat');
define('URL_SISTEM', 'http://localhost/jkndata');
define('EMEL_ADMIN', '<EMAIL>');
define('VERSI_SISTEM', '1.0.0');

// Tetapan pangkalan data
require_once 'database.php';

// Kelas untuk menguruskan tetapan sistem
class SistemConfig {
    private $db;
    private $tetapan = [];

    public function __construct($database) {
        $this->db = $database;
        $this->muatkanTetapan();
    }

    private function muatkanTetapan() {
        try {
            $sql = "SELECT nama_tetapan, nilai, jenis FROM tetapan_sistem";
            $result = $this->db->fetchAll($sql);
            
            foreach ($result as $row) {
                $nilai = $row['nilai'];
                
                // Tukar nilai mengikut jenis
                switch ($row['jenis']) {
                    case 'nombor':
                        $nilai = (int)$nilai;
                        break;
                    case 'boolean':
                        $nilai = ($nilai === 'true' || $nilai === '1');
                        break;
                    case 'json':
                        $nilai = json_decode($nilai, true);
                        break;
                }
                
                $this->tetapan[$row['nama_tetapan']] = $nilai;
            }
        } catch (Exception $e) {
            // Jika jadual tetapan belum wujud, gunakan tetapan lalai
            $this->tetapanLalai();
        }
    }

    private function tetapanLalai() {
        $this->tetapan = [
            'nama_organisasi' => 'Sistem Tempahan Bilik Mesyuarat',
            'masa_tempahan_minimum' => 30,
            'masa_tempahan_maksimum' => 480,
            'hari_tempahan_awal' => 30,
            'emel_notifikasi' => true,
            'kelulusan_automatik' => false
        ];
    }

    public function dapatkan($nama_tetapan, $nilai_lalai = null) {
        return isset($this->tetapan[$nama_tetapan]) ? $this->tetapan[$nama_tetapan] : $nilai_lalai;
    }

    public function tetapkan($nama_tetapan, $nilai, $jenis = 'teks') {
        try {
            // Tukar nilai kepada string untuk disimpan
            $nilai_string = $nilai;
            if ($jenis === 'boolean') {
                $nilai_string = $nilai ? 'true' : 'false';
            } elseif ($jenis === 'json') {
                $nilai_string = json_encode($nilai);
            }

            $sql = "INSERT INTO tetapan_sistem (nama_tetapan, nilai, jenis) 
                    VALUES (?, ?, ?) 
                    ON DUPLICATE KEY UPDATE nilai = ?, jenis = ?";
            
            $this->db->query($sql, [$nama_tetapan, $nilai_string, $jenis, $nilai_string, $jenis]);
            $this->tetapan[$nama_tetapan] = $nilai;
            
            return true;
        } catch (Exception $e) {
            return false;
        }
    }

    public function semuaTetapan() {
        return $this->tetapan;
    }
}

// Fungsi bantuan untuk keselamatan
function bersihkanInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

function semakLogin() {
    return isset($_SESSION['pengguna_id']) && !empty($_SESSION['pengguna_id']) &&
           isset($_SESSION['nokp']) && !empty($_SESSION['nokp']);
}

function semakPeranan($peranan_diperlukan) {
    if (!semakLogin()) {
        return false;
    }
    
    $peranan_pengguna = $_SESSION['peranan'] ?? '';
    
    if ($peranan_diperlukan === 'pentadbir') {
        return $peranan_pengguna === 'pentadbir';
    } elseif ($peranan_diperlukan === 'penyelaras') {
        return in_array($peranan_pengguna, ['pentadbir', 'penyelaras']);
    }
    
    return true; // Pengguna biasa
}

function perluLogin() {
    if (!semakLogin()) {
        arahkanKeLogin();
    }
}

function perluPentadbir() {
    perluLogin();
    if (!semakPeranan('pentadbir')) {
        arahkanKe(URL_SISTEM . '/index.php');
    }
}

function arahkanKeLogin() {
    header('Location: ' . URL_SISTEM . '/login.php');
    exit();
}

function arahkanKe($url) {
    header('Location: ' . $url);
    exit();
}

function paparMesej($jenis, $mesej) {
    $kelas = '';
    $ikon = '';
    
    switch ($jenis) {
        case 'kejayaan':
            $kelas = 'alert-success';
            $ikon = 'bi-check-circle';
            break;
        case 'ralat':
            $kelas = 'alert-danger';
            $ikon = 'bi-exclamation-triangle';
            break;
        case 'amaran':
            $kelas = 'alert-warning';
            $ikon = 'bi-exclamation-circle';
            break;
        default:
            $kelas = 'alert-info';
            $ikon = 'bi-info-circle';
    }
    
    return "<div class='alert {$kelas} alert-dismissible fade show' role='alert'>
                <i class='bi {$ikon} me-2'></i>{$mesej}
                <button type='button' class='btn-close' data-bs-dismiss='alert'></button>
            </div>";
}

function formatTarikh($tarikh, $format = 'd/m/Y') {
    if (empty($tarikh)) return '';
    return date($format, strtotime($tarikh));
}

function formatMasa($masa, $format = 'H:i') {
    if (empty($masa)) return '';
    return date($format, strtotime($masa));
}

function formatTarikhMasa($tarikh_masa, $format = 'd/m/Y H:i') {
    if (empty($tarikh_masa)) return '';
    return date($format, strtotime($tarikh_masa));
}

function formatWang($jumlah) {
    return 'RM ' . number_format($jumlah, 2);
}

function janaKodTempahan() {
    return 'TMP' . date('Ymd') . rand(1000, 9999);
}

function janaMasaDropdown($nama, $nilai_terpilih = '', $langkah = 30, $mula = 8, $tamat = 18) {
    $html = "<select name='{$nama}' id='{$nama}' class='form-select' required>";
    $html .= "<option value=''>Pilih Masa</option>";
    
    for ($jam = $mula; $jam <= $tamat; $jam++) {
        for ($minit = 0; $minit < 60; $minit += $langkah) {
            if ($jam == $tamat && $minit > 0) break; // Jangan tambah minit untuk jam terakhir
            
            $masa = sprintf('%02d:%02d', $jam, $minit);
            $selected = ($masa === $nilai_terpilih) ? 'selected' : '';
            $html .= "<option value='{$masa}' {$selected}>{$masa}</option>";
        }
    }
    
    $html .= "</select>";
    return $html;
}

// Inisialisasi konfigurasi sistem
try {
    $config = new SistemConfig($db);
} catch (Exception $e) {
    // Jika pangkalan data belum siap, gunakan tetapan lalai
    $config = null;
}

// Tetapan mesej flash
function setMesejFlash($jenis, $mesej) {
    $_SESSION['flash_message'] = [
        'jenis' => $jenis,
        'mesej' => $mesej
    ];
}

function dapatkanMesejFlash() {
    if (isset($_SESSION['flash_message'])) {
        $mesej = $_SESSION['flash_message'];
        unset($_SESSION['flash_message']);
        return $mesej;
    }
    return null;
}

// Fungsi untuk hantar notifikasi
function hantarNotifikasi($pengguna_id, $tajuk, $mesej, $jenis = 'maklumat', $tempahan_id = null) {
    global $db;
    
    try {
        $sql = "INSERT INTO notifikasi (pengguna_id, tajuk, mesej, jenis, tempahan_id) 
                VALUES (?, ?, ?, ?, ?)";
        $db->query($sql, [$pengguna_id, $tajuk, $mesej, $jenis, $tempahan_id]);
        return true;
    } catch (Exception $e) {
        return false;
    }
}

// Fungsi untuk log aktiviti
function logAktiviti($pengguna_id, $aktiviti, $butiran = '') {
    global $db;
    
    try {
        // Buat jadual log jika belum wujud
        $sql_create = "CREATE TABLE IF NOT EXISTS log_aktiviti (
            id INT AUTO_INCREMENT PRIMARY KEY,
            pengguna_id INT,
            aktiviti VARCHAR(255),
            butiran TEXT,
            tarikh_masa TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        $db->query($sql_create);
        
        $sql = "INSERT INTO log_aktiviti (pengguna_id, aktiviti, butiran) 
                VALUES (?, ?, ?)";
        $db->query($sql, [$pengguna_id, $aktiviti, $butiran]);
    } catch (Exception $e) {
        // Abaikan ralat log
    }
}

// Fungsi untuk validasi CSRF token
function janaTokenCSRF() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

function validasiTokenCSRF($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

// Fungsi untuk muatkan kelas secara automatik
spl_autoload_register(function ($class) {
    $file = __DIR__ . '/../classes/' . $class . '.php';
    if (file_exists($file)) {
        require_once $file;
    }
});
?>
