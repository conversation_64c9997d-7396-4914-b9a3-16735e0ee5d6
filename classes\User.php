<?php
/**
 * User Class
 * Handles user authentication and management
 */

require_once __DIR__ . '/../config/config.php';

class User {
    private $db;

    public function __construct() {
        global $db;
        $this->db = $db;
    }

    public function register($username, $email, $password, $full_name, $phone = null) {
        try {
            // Check if username or email already exists
            $existing = $this->db->fetch(
                "SELECT id FROM users WHERE username = ? OR email = ?",
                [$username, $email]
            );

            if ($existing) {
                return ['success' => false, 'message' => 'Username or email already exists'];
            }

            // Hash password
            $hashedPassword = password_hash($password, HASH_ALGO);

            // Insert new user
            $sql = "INSERT INTO users (username, email, password, full_name, phone) VALUES (?, ?, ?, ?, ?)";
            $this->db->query($sql, [$username, $email, $hashedPassword, $full_name, $phone]);

            return ['success' => true, 'message' => 'Registration successful'];
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Registration failed: ' . $e->getMessage()];
        }
    }

    public function login($username, $password) {
        try {
            $user = $this->db->fetch(
                "SELECT * FROM users WHERE username = ? OR email = ?",
                [$username, $username]
            );

            if ($user && password_verify($password, $user['password'])) {
                // Set session variables
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['full_name'] = $user['full_name'];
                $_SESSION['role'] = $user['role'];
                $_SESSION['login_time'] = time();

                return ['success' => true, 'message' => 'Login successful'];
            } else {
                return ['success' => false, 'message' => 'Invalid username or password'];
            }
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Login failed: ' . $e->getMessage()];
        }
    }

    public function logout() {
        session_destroy();
        return ['success' => true, 'message' => 'Logged out successfully'];
    }

    public function getCurrentUser() {
        if (!isLoggedIn()) {
            return null;
        }

        return $this->db->fetch(
            "SELECT id, username, email, full_name, phone, role, created_at FROM users WHERE id = ?",
            [$_SESSION['user_id']]
        );
    }

    public function updateProfile($user_id, $email, $full_name, $phone) {
        try {
            $sql = "UPDATE users SET email = ?, full_name = ?, phone = ? WHERE id = ?";
            $this->db->query($sql, [$email, $full_name, $phone, $user_id]);

            return ['success' => true, 'message' => 'Profile updated successfully'];
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Update failed: ' . $e->getMessage()];
        }
    }

    public function changePassword($user_id, $current_password, $new_password) {
        try {
            $user = $this->db->fetch("SELECT password FROM users WHERE id = ?", [$user_id]);

            if (!$user || !password_verify($current_password, $user['password'])) {
                return ['success' => false, 'message' => 'Current password is incorrect'];
            }

            $hashedPassword = password_hash($new_password, HASH_ALGO);
            $this->db->query("UPDATE users SET password = ? WHERE id = ?", [$hashedPassword, $user_id]);

            return ['success' => true, 'message' => 'Password changed successfully'];
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Password change failed: ' . $e->getMessage()];
        }
    }

    public function getAllUsers() {
        return $this->db->fetchAll(
            "SELECT id, username, email, full_name, phone, role, created_at FROM users ORDER BY created_at DESC"
        );
    }

    public function getUserById($id) {
        return $this->db->fetch(
            "SELECT id, username, email, full_name, phone, role, created_at FROM users WHERE id = ?",
            [$id]
        );
    }

    public function updateUserRole($user_id, $role) {
        try {
            $this->db->query("UPDATE users SET role = ? WHERE id = ?", [$role, $user_id]);
            return ['success' => true, 'message' => 'User role updated successfully'];
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Role update failed: ' . $e->getMessage()];
        }
    }

    public function deleteUser($user_id) {
        try {
            $this->db->query("DELETE FROM users WHERE id = ?", [$user_id]);
            return ['success' => true, 'message' => 'User deleted successfully'];
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Delete failed: ' . $e->getMessage()];
        }
    }

    public function checkSessionTimeout() {
        if (isLoggedIn() && isset($_SESSION['login_time'])) {
            if (time() - $_SESSION['login_time'] > SESSION_TIMEOUT) {
                $this->logout();
                return false;
            }
            $_SESSION['login_time'] = time(); // Refresh session time
        }
        return true;
    }
}
?>
