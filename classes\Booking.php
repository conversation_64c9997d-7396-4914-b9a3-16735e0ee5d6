<?php
/**
 * Booking Class
 * Handles meeting room bookings
 */

require_once __DIR__ . '/../config/config.php';

class Booking {
    private $db;

    public function __construct() {
        global $db;
        $this->db = $db;
    }

    public function createBooking($data) {
        try {
            $this->db->beginTransaction();

            // Check room availability
            if (!$this->isRoomAvailable($data['room_id'], $data['booking_date'], $data['start_time'], $data['end_time'])) {
                throw new Exception('Room is not available for the selected time slot');
            }

            // Calculate total cost
            $room = $this->db->fetch("SELECT hourly_rate FROM meeting_rooms WHERE id = ?", [$data['room_id']]);
            $duration = $this->calculateDuration($data['start_time'], $data['end_time']);
            $total_cost = $room['hourly_rate'] * $duration;

            // Insert booking
            $sql = "INSERT INTO bookings (user_id, room_id, booking_date, start_time, end_time, purpose, attendees, total_cost, notes) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            $this->db->query($sql, [
                $data['user_id'],
                $data['room_id'],
                $data['booking_date'],
                $data['start_time'],
                $data['end_time'],
                $data['purpose'],
                $data['attendees'],
                $total_cost,
                $data['notes'] ?? null
            ]);

            $booking_id = $this->db->lastInsertId();

            // Log booking history
            $this->logBookingHistory($booking_id, 'created', $data['user_id'], null, $data);

            $this->db->commit();
            return ['success' => true, 'message' => 'Booking created successfully', 'booking_id' => $booking_id];
        } catch (Exception $e) {
            $this->db->rollback();
            return ['success' => false, 'message' => 'Booking failed: ' . $e->getMessage()];
        }
    }

    public function updateBooking($booking_id, $data, $user_id) {
        try {
            $this->db->beginTransaction();

            // Get current booking
            $current_booking = $this->getBookingById($booking_id);
            if (!$current_booking) {
                throw new Exception('Booking not found');
            }

            // Check if user can modify this booking
            if ($current_booking['user_id'] != $user_id && !isAdmin()) {
                throw new Exception('You can only modify your own bookings');
            }

            // Check room availability (excluding current booking)
            if (!$this->isRoomAvailable($data['room_id'], $data['booking_date'], $data['start_time'], $data['end_time'], $booking_id)) {
                throw new Exception('Room is not available for the selected time slot');
            }

            // Calculate new total cost
            $room = $this->db->fetch("SELECT hourly_rate FROM meeting_rooms WHERE id = ?", [$data['room_id']]);
            $duration = $this->calculateDuration($data['start_time'], $data['end_time']);
            $total_cost = $room['hourly_rate'] * $duration;

            // Update booking
            $sql = "UPDATE bookings SET room_id = ?, booking_date = ?, start_time = ?, end_time = ?, 
                    purpose = ?, attendees = ?, total_cost = ?, notes = ? WHERE id = ?";
            
            $this->db->query($sql, [
                $data['room_id'],
                $data['booking_date'],
                $data['start_time'],
                $data['end_time'],
                $data['purpose'],
                $data['attendees'],
                $total_cost,
                $data['notes'] ?? null,
                $booking_id
            ]);

            // Log booking history
            $this->logBookingHistory($booking_id, 'updated', $user_id, $current_booking, $data);

            $this->db->commit();
            return ['success' => true, 'message' => 'Booking updated successfully'];
        } catch (Exception $e) {
            $this->db->rollback();
            return ['success' => false, 'message' => 'Update failed: ' . $e->getMessage()];
        }
    }

    public function cancelBooking($booking_id, $user_id) {
        try {
            $booking = $this->getBookingById($booking_id);
            if (!$booking) {
                return ['success' => false, 'message' => 'Booking not found'];
            }

            // Check if user can cancel this booking
            if ($booking['user_id'] != $user_id && !isAdmin()) {
                return ['success' => false, 'message' => 'You can only cancel your own bookings'];
            }

            // Check if booking can be cancelled (not in the past)
            $booking_datetime = $booking['booking_date'] . ' ' . $booking['start_time'];
            if (strtotime($booking_datetime) <= time()) {
                return ['success' => false, 'message' => 'Cannot cancel past bookings'];
            }

            $this->db->query("UPDATE bookings SET status = 'cancelled' WHERE id = ?", [$booking_id]);
            
            // Log booking history
            $this->logBookingHistory($booking_id, 'cancelled', $user_id, $booking, ['status' => 'cancelled']);

            return ['success' => true, 'message' => 'Booking cancelled successfully'];
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Cancellation failed: ' . $e->getMessage()];
        }
    }

    public function confirmBooking($booking_id, $user_id) {
        try {
            $this->db->query("UPDATE bookings SET status = 'confirmed' WHERE id = ?", [$booking_id]);
            
            $booking = $this->getBookingById($booking_id);
            $this->logBookingHistory($booking_id, 'confirmed', $user_id, $booking, ['status' => 'confirmed']);

            return ['success' => true, 'message' => 'Booking confirmed successfully'];
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Confirmation failed: ' . $e->getMessage()];
        }
    }

    public function getUserBookings($user_id, $status = null, $limit = null) {
        $sql = "SELECT b.*, r.room_name, r.location, u.full_name 
                FROM bookings b 
                JOIN meeting_rooms r ON b.room_id = r.id 
                JOIN users u ON b.user_id = u.id 
                WHERE b.user_id = ?";
        $params = [$user_id];

        if ($status) {
            $sql .= " AND b.status = ?";
            $params[] = $status;
        }

        $sql .= " ORDER BY b.booking_date DESC, b.start_time DESC";

        if ($limit) {
            $sql .= " LIMIT ?";
            $params[] = $limit;
        }

        return $this->db->fetchAll($sql, $params);
    }

    public function getAllBookings($status = null, $date_from = null, $date_to = null) {
        $sql = "SELECT b.*, r.room_name, r.location, u.full_name 
                FROM bookings b 
                JOIN meeting_rooms r ON b.room_id = r.id 
                JOIN users u ON b.user_id = u.id 
                WHERE 1=1";
        $params = [];

        if ($status) {
            $sql .= " AND b.status = ?";
            $params[] = $status;
        }

        if ($date_from) {
            $sql .= " AND b.booking_date >= ?";
            $params[] = $date_from;
        }

        if ($date_to) {
            $sql .= " AND b.booking_date <= ?";
            $params[] = $date_to;
        }

        $sql .= " ORDER BY b.booking_date DESC, b.start_time DESC";

        return $this->db->fetchAll($sql, $params);
    }

    public function getBookingById($id) {
        return $this->db->fetch(
            "SELECT b.*, r.room_name, r.location, r.hourly_rate, u.full_name, u.email, u.phone 
             FROM bookings b 
             JOIN meeting_rooms r ON b.room_id = r.id 
             JOIN users u ON b.user_id = u.id 
             WHERE b.id = ?",
            [$id]
        );
    }

    public function getRoomBookings($room_id, $date) {
        return $this->db->fetchAll(
            "SELECT b.*, u.full_name 
             FROM bookings b 
             JOIN users u ON b.user_id = u.id 
             WHERE b.room_id = ? AND b.booking_date = ? AND b.status != 'cancelled' 
             ORDER BY b.start_time",
            [$room_id, $date]
        );
    }

    private function isRoomAvailable($room_id, $date, $start_time, $end_time, $exclude_booking_id = null) {
        $sql = "SELECT COUNT(*) as count FROM bookings 
                WHERE room_id = ? AND booking_date = ? AND status != 'cancelled'
                AND ((start_time < ? AND end_time > ?) OR (start_time < ? AND end_time > ?) OR (start_time >= ? AND end_time <= ?))";
        $params = [$room_id, $date, $end_time, $start_time, $start_time, $end_time, $start_time, $end_time];

        if ($exclude_booking_id) {
            $sql .= " AND id != ?";
            $params[] = $exclude_booking_id;
        }

        $result = $this->db->fetch($sql, $params);
        return $result['count'] == 0;
    }

    private function calculateDuration($start_time, $end_time) {
        $start = strtotime($start_time);
        $end = strtotime($end_time);
        return ($end - $start) / 3600; // Convert to hours
    }

    private function logBookingHistory($booking_id, $action, $changed_by, $old_values, $new_values) {
        $sql = "INSERT INTO booking_history (booking_id, action, changed_by, old_values, new_values) VALUES (?, ?, ?, ?, ?)";
        $this->db->query($sql, [
            $booking_id,
            $action,
            $changed_by,
            $old_values ? json_encode($old_values) : null,
            json_encode($new_values)
        ]);
    }

    public function getBookingStats() {
        $stats = [];
        
        // Total bookings
        $stats['total'] = $this->db->fetch("SELECT COUNT(*) as count FROM bookings")['count'];
        
        // Today's bookings
        $stats['today'] = $this->db->fetch(
            "SELECT COUNT(*) as count FROM bookings WHERE booking_date = CURDATE()"
        )['count'];
        
        // This month's bookings
        $stats['month'] = $this->db->fetch(
            "SELECT COUNT(*) as count FROM bookings WHERE MONTH(booking_date) = MONTH(CURDATE()) AND YEAR(booking_date) = YEAR(CURDATE())"
        )['count'];
        
        // Pending bookings
        $stats['pending'] = $this->db->fetch(
            "SELECT COUNT(*) as count FROM bookings WHERE status = 'pending'"
        )['count'];

        return $stats;
    }
}
?>
