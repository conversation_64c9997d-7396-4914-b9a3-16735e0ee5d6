/* CSS Sistem Cantik - Dengan Animation Minimal */

/* Benarkan transition yang diperlukan */
.btn, .card, .nav-link, .dropdown-item, .form-control, .form-select {
    transition: all 0.3s ease;
}

/* Card hover effects yang cantik */
.card {
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-radius: 15px;
    overflow: hidden;
}

.card:hover {
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

/* But<PERSON> styles yang cantik */
.btn {
    border-radius: 10px;
    font-weight: 500;
    padding: 0.5rem 1.5rem;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

/* Gradient backgrounds */
.bg-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.bg-danger {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
}

.bg-success {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
}

.bg-warning {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%) !important;
}

.bg-info {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%) !important;
}

/* Table hover yang simple */
.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.075);
    transform: none !important;
}

/* Dropdown yang simple */
.dropdown-menu {
    transform: none !important;
    animation: none !important;
}

/* Modal yang simple */
.modal.fade .modal-dialog {
    transform: none !important;
    transition: none !important;
}

.modal.show .modal-dialog {
    transform: none !important;
}

/* Navbar yang simple */
.navbar-nav .nav-link:hover {
    transform: none !important;
    background-color: rgba(255, 255, 255, 0.1);
}

/* Form controls yang simple */
.form-control:focus, .form-select:focus {
    transform: none !important;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Alert yang simple */
.alert {
    transform: none !important;
    animation: none !important;
}

/* Badge yang simple */
.badge {
    transform: none !important;
}

/* Progress bar yang simple */
.progress-bar {
    transition: width 0.6s ease !important;
    transform: none !important;
}

/* Spinner yang diperlukan sahaja */
.spinner-border {
    animation: spinner-border 0.75s linear infinite !important;
}

@keyframes spinner-border {
    to {
        transform: rotate(360deg);
    }
}

/* Collapse yang simple */
.collapse {
    transition: height 0.35s ease !important;
    transform: none !important;
}

/* Tooltip yang simple */
.tooltip {
    transform: none !important;
    animation: none !important;
}

/* Popover yang simple */
.popover {
    transform: none !important;
    animation: none !important;
}

/* Custom styles untuk sistem */
.sistem-card {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    background-color: #fff;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.sistem-card:hover {
    border-color: #86b7fe;
    background-color: #fff;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.sistem-btn {
    border: 1px solid transparent;
    border-radius: 0.375rem;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    line-height: 1.5;
    text-decoration: none;
    display: inline-block;
    text-align: center;
    vertical-align: middle;
    cursor: pointer;
}

.sistem-btn:hover {
    text-decoration: none;
}

.sistem-btn-primary {
    color: #fff;
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.sistem-btn-primary:hover {
    color: #fff;
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

/* Table yang bersih */
.sistem-table {
    width: 100%;
    margin-bottom: 1rem;
    color: #212529;
    border-collapse: collapse;
}

.sistem-table th,
.sistem-table td {
    padding: 0.75rem;
    vertical-align: top;
    border-top: 1px solid #dee2e6;
}

.sistem-table thead th {
    vertical-align: bottom;
    border-bottom: 2px solid #dee2e6;
    background-color: #f8f9fa;
}

.sistem-table tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.075);
}

/* Navigation yang bersih */
.sistem-nav {
    background-color: #0d6efd;
    padding: 1rem 0;
}

.sistem-nav .nav-link {
    color: rgba(255, 255, 255, 0.75);
    padding: 0.5rem 1rem;
    text-decoration: none;
}

.sistem-nav .nav-link:hover {
    color: #fff;
    background-color: rgba(255, 255, 255, 0.1);
}

.sistem-nav .nav-link.active {
    color: #fff;
    background-color: rgba(255, 255, 255, 0.2);
}

/* Form yang bersih */
.sistem-form-control {
    display: block;
    width: 100%;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #212529;
    background-color: #fff;
    background-image: none;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
}

.sistem-form-control:focus {
    color: #212529;
    background-color: #fff;
    border-color: #86b7fe;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Utilities */
.sistem-text-center { text-align: center; }
.sistem-text-left { text-align: left; }
.sistem-text-right { text-align: right; }
.sistem-d-none { display: none; }
.sistem-d-block { display: block; }
.sistem-d-flex { display: flex; }
.sistem-justify-content-center { justify-content: center; }
.sistem-align-items-center { align-items: center; }
.sistem-mb-0 { margin-bottom: 0; }
.sistem-mb-1 { margin-bottom: 0.25rem; }
.sistem-mb-2 { margin-bottom: 0.5rem; }
.sistem-mb-3 { margin-bottom: 1rem; }
.sistem-mb-4 { margin-bottom: 1.5rem; }
.sistem-mb-5 { margin-bottom: 3rem; }
.sistem-mt-0 { margin-top: 0; }
.sistem-mt-1 { margin-top: 0.25rem; }
.sistem-mt-2 { margin-top: 0.5rem; }
.sistem-mt-3 { margin-top: 1rem; }
.sistem-mt-4 { margin-top: 1.5rem; }
.sistem-mt-5 { margin-top: 3rem; }
.sistem-p-0 { padding: 0; }
.sistem-p-1 { padding: 0.25rem; }
.sistem-p-2 { padding: 0.5rem; }
.sistem-p-3 { padding: 1rem; }
.sistem-p-4 { padding: 1.5rem; }
.sistem-p-5 { padding: 3rem; }

/* Responsive */
@media (max-width: 768px) {
    .sistem-card {
        margin-bottom: 1rem;
    }
    
    .sistem-table {
        font-size: 0.875rem;
    }
    
    .sistem-btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }
}
