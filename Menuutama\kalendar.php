<?php
/**
 * Kalendar Tempahan
 * Sistem Tempahan Bilik Mesyuarat
 */

$tajuk_halaman = 'Kalendar Tempahan';
require_once '../config/sistem_config.php';

// Semak login
perluLogin();

// Parameter bulan dan tahun
$bulan = $_GET['bulan'] ?? date('m');
$tahun = $_GET['tahun'] ?? date('Y');
$bilik_filter = $_GET['bilik'] ?? '';

// Validasi input
$bulan = (int)$bulan;
$tahun = (int)$tahun;
if ($bulan < 1 || $bulan > 12) $bulan = date('m');
if ($tahun < 2020 || $tahun > 2030) $tahun = date('Y');

// Set default bilik jika tiada dipilih
if (empty($bilik_filter) && !empty($senarai_bilik)) {
    $bilik_filter = $senarai_bilik[0]['id'];
}

// Dapatkan tempahan untuk bulan ini
try {
    $where_conditions = ["DATE_FORMAT(t.tarikh_mula, '%Y-%m') = ?"];
    $params = [sprintf('%04d-%02d', $tahun, $bulan)];

    if (!empty($bilik_filter)) {
        $where_conditions[] = "t.idbilik_mesyuarat = ?";
        $params[] = $bilik_filter;
    }

    // Semua pengguna boleh lihat semua tempahan di kalendar
    // Tiada sekatan berdasarkan peranan atau pemilikan
    
    $where_clause = implode(' AND ', $where_conditions);
    
    $sql = "SELECT t.*, b.nama_bilik_mesyuarat as nama_bilik, b.lokasi, p.nama_penuh as nama_pengguna
            FROM ttempahan t
            JOIN tbilik_mesyuarat b ON t.idbilik_mesyuarat = b.id
            JOIN pengguna p ON t.idpemohon = p.id
            WHERE $where_clause
            AND t.kelulusan = 'LULUS'
            AND (t.batal_tempahan IS NULL OR t.batal_tempahan != 'BATAL')
            ORDER BY t.tarikh_mula, t.tarikh_mula";
    
    $tempahan_bulan = $db->fetchAll($sql, $params);

    // Debug: Uncomment to see what data is fetched
    // echo "<pre>Debug - SQL: " . $sql . "\nParams: " . print_r($params, true) . "\nResults: " . print_r($tempahan_bulan, true) . "</pre>";
    
    // Susun tempahan mengikut tarikh
    $tempahan_by_date = [];
    foreach ($tempahan_bulan as $tempahan) {
        // Extract date from datetime field
        $tarikh = date('Y-m-d', strtotime($tempahan['tarikh_mula']));
        if (!isset($tempahan_by_date[$tarikh])) {
            $tempahan_by_date[$tarikh] = [];
        }
        // Add time fields for display
        $tempahan['masa_mula'] = date('H:i', strtotime($tempahan['tarikh_mula']));
        $tempahan['masa_tamat'] = date('H:i', strtotime($tempahan['tarikh_tamat']));
        $tempahan['status'] = strtolower($tempahan['kelulusan']); // Convert to lowercase for compatibility
        $tempahan['tujuan'] = $tempahan['tajuk_mesyuarat']; // Use tajuk_mesyuarat as tujuan
        $tempahan_by_date[$tarikh][] = $tempahan;
    }
    
    // Dapatkan senarai bilik untuk filter
    $sql_bilik = "SELECT id, nama_bilik_mesyuarat AS nama_bilik FROM tbilik_mesyuarat WHERE status = 'tersedia' ORDER BY nama_bilik_mesyuarat";
    $senarai_bilik = $db->fetchAll($sql_bilik);
    
} catch (Exception $e) {
    $tempahan_by_date = [];
    $senarai_bilik = [];
    setMesejFlash('ralat', 'Ralat memuatkan data kalendar: ' . $e->getMessage());
}

// Fungsi untuk jana kalendar
function janaKalendar($bulan, $tahun, $tempahan_by_date) {
    $hari_pertama = mktime(0, 0, 0, $bulan, 1, $tahun);
    $nama_bulan = date('F Y', $hari_pertama);
    $hari_dalam_bulan = date('t', $hari_pertama);
    $hari_minggu_pertama = date('w', $hari_pertama);
    
    $kalendar = "<div class='calendar-grid'>";
    
    // Header hari
    $nama_hari = ['Ahd', 'Isn', 'Sel', 'Rab', 'Kha', 'Jum', 'Sab'];
    foreach ($nama_hari as $hari) {
        $kalendar .= "<div class='calendar-header'>$hari</div>";
    }
    
    // Hari kosong sebelum hari pertama
    for ($i = 0; $i < $hari_minggu_pertama; $i++) {
        $kalendar .= "<div class='calendar-day empty'></div>";
    }
    
    // Hari dalam bulan
    for ($hari = 1; $hari <= $hari_dalam_bulan; $hari++) {
        $tarikh = sprintf('%04d-%02d-%02d', $tahun, $bulan, $hari);
        $hari_ini = ($tarikh == date('Y-m-d')) ? 'today' : '';
        $ada_tempahan = isset($tempahan_by_date[$tarikh]) ? 'has-booking' : '';

        // Format tarikh untuk URL (YYYY-MM-DD)
        $tarikh_url = $tarikh;

        $kalendar .= "<div class='calendar-day $hari_ini $ada_tempahan clickable-day' data-date='$tarikh_url' onclick='lihatTempahanTarikh(\"$tarikh_url\")'>";
        $kalendar .= "<div class='day-number'>$hari</div>";

        if (isset($tempahan_by_date[$tarikh])) {
            $jumlah = count($tempahan_by_date[$tarikh]);
            $kalendar .= "<div class='booking-count'>$jumlah tempahan</div>";
        }

        // Tambah ikon untuk menunjukkan boleh klik
        $kalendar .= "<div class='click-hint'><i class='bi bi-plus-circle'></i></div>";

        $kalendar .= "</div>";
    }
    
    $kalendar .= "</div>";
    return $kalendar;
}

require_once '../includes/header_sistem.php';
?>

<style>
.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    background-color: #dee2e6;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    overflow: hidden;
}

.calendar-header {
    background-color: var(--primary-color);
    color: white;
    padding: 10px;
    text-align: center;
    font-weight: 600;
    font-size: 0.9rem;
}

.calendar-day {
    background-color: white;
    min-height: 80px;
    padding: 8px;
    transition: all 0.2s;
    position: relative;
}

.calendar-day.clickable-day {
    cursor: pointer;
}

.calendar-day.clickable-day:hover {
    background-color: #e3f2fd;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.calendar-day.clickable-day:hover .click-hint {
    opacity: 1;
}

.calendar-day.empty {
    background-color: #f8f9fa;
    cursor: default;
}

.calendar-day.today {
    background-color: #e3f2fd;
    border: 2px solid var(--primary-color);
}

.calendar-day.has-booking {
    background-color: #fff3cd;
}

.calendar-day.has-booking:hover {
    background-color: #ffeaa7;
}

.day-number {
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 4px;
}

.booking-count {
    font-size: 0.75rem;
    color: #6c757d;
    background-color: rgba(0,0,0,0.1);
    padding: 2px 6px;
    border-radius: 10px;
    text-align: center;
}

.calendar-day.today .day-number {
    color: var(--primary-color);
}

.click-hint {
    position: absolute;
    top: 4px;
    right: 4px;
    opacity: 0;
    transition: opacity 0.2s;
    color: var(--primary-color);
    font-size: 0.8rem;
}

.calendar-day.clickable-day .click-hint {
    opacity: 0.3;
}

@media (max-width: 768px) {
    .calendar-day {
        min-height: 60px;
        padding: 4px;
    }

    .day-number {
        font-size: 1rem;
    }

    .booking-count {
        font-size: 0.7rem;
    }

    .click-hint {
        font-size: 0.7rem;
    }
}
</style>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0">Kalendar Tempahan</h1>
                <p class="text-muted mb-0">Lihat tempahan bilik mesyuarat dalam bentuk kalendar</p>
            </div>
            <div>
                <a href="../BuatTempahan/tempah_bilik.php" class="btn btn-primary">
                    <i class="bi bi-calendar-plus me-2"></i>Tempahan Baru
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Kawalan Kalendar -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="">
            <div class="row g-3 align-items-end">
                <div class="col-md-2">
                    <label for="bulan" class="form-label">Bulan</label>
                    <select class="form-select" id="bulan" name="bulan">
                        <?php
                        $nama_bulan = [
                            1 => 'Januari', 2 => 'Februari', 3 => 'Mac', 4 => 'April',
                            5 => 'Mei', 6 => 'Jun', 7 => 'Julai', 8 => 'Ogos',
                            9 => 'September', 10 => 'Oktober', 11 => 'November', 12 => 'Disember'
                        ];
                        for ($i = 1; $i <= 12; $i++):
                        ?>
                            <option value="<?= $i ?>" <?= ($i == $bulan) ? 'selected' : '' ?>>
                                <?= $nama_bulan[$i] ?>
                            </option>
                        <?php endfor; ?>
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label for="tahun" class="form-label">Tahun</label>
                    <select class="form-select" id="tahun" name="tahun">
                        <?php for ($y = 2020; $y <= 2030; $y++): ?>
                            <option value="<?= $y ?>" <?= ($y == $tahun) ? 'selected' : '' ?>><?= $y ?></option>
                        <?php endfor; ?>
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label for="bilik" class="form-label">Bilik</label>
                    <select class="form-select" id="bilik" name="bilik">
                        <?php foreach ($senarai_bilik as $bilik): ?>
                            <option value="<?= $bilik['id'] ?>" <?= ($bilik['id'] == $bilik_filter) ? 'selected' : '' ?>>
                                <?= htmlspecialchars($bilik['nama_bilik']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="bi bi-search"></i> Papar
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Kalendar -->
<div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="bi bi-calendar3 me-2"></i>
            <?php
            $nama_bulan_display = [
                1 => 'Januari', 2 => 'Februari', 3 => 'Mac', 4 => 'April',
                5 => 'Mei', 6 => 'Jun', 7 => 'Julai', 8 => 'Ogos',
                9 => 'September', 10 => 'Oktober', 11 => 'November', 12 => 'Disember'
            ];
            echo $nama_bulan_display[$bulan] . ' ' . $tahun;
            ?>
        </h5>
        
        <div class="btn-group btn-group-sm">
            <?php
            $bulan_sebelum = $bulan - 1;
            $tahun_sebelum = $tahun;
            if ($bulan_sebelum < 1) {
                $bulan_sebelum = 12;
                $tahun_sebelum--;
            }
            
            $bulan_seterusnya = $bulan + 1;
            $tahun_seterusnya = $tahun;
            if ($bulan_seterusnya > 12) {
                $bulan_seterusnya = 1;
                $tahun_seterusnya++;
            }
            ?>
            <a href="?bulan=<?= $bulan_sebelum ?>&tahun=<?= $tahun_sebelum ?>&bilik=<?= $bilik_filter ?>" 
               class="btn btn-outline-primary">
                <i class="bi bi-chevron-left"></i>
            </a>
            <a href="?bulan=<?= $bulan_seterusnya ?>&tahun=<?= $tahun_seterusnya ?>&bilik=<?= $bilik_filter ?>" 
               class="btn btn-outline-primary">
                <i class="bi bi-chevron-right"></i>
            </a>
        </div>
    </div>
    
    <div class="card-body p-0">
        <?= janaKalendar($bulan, $tahun, $tempahan_by_date) ?>
    </div>
</div>

<!-- Legenda -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h6><i class="bi bi-info-circle me-2"></i>Legenda</h6>
                <div class="d-flex flex-wrap gap-3">
                    <div class="d-flex align-items-center">
                        <div class="calendar-day today me-2" style="width: 30px; height: 30px; min-height: 30px; border-radius: 4px;"></div>
                        <small>Hari Ini</small>
                    </div>
                    <div class="d-flex align-items-center">
                        <div class="calendar-day has-booking me-2" style="width: 30px; height: 30px; min-height: 30px; border-radius: 4px;"></div>
                        <small>Ada Tempahan</small>
                    </div>
                    <div class="d-flex align-items-center">
                        <div class="calendar-day me-2" style="width: 30px; height: 30px; min-height: 30px; border-radius: 4px; background-color: white; border: 1px solid #dee2e6;"></div>
                        <small>Tiada Tempahan</small>
                    </div>
                    <div class="d-flex align-items-center">
                        <div class="me-2" style="width: 30px; height: 30px; min-height: 30px; border-radius: 4px; background-color: #e3f2fd; border: 1px solid var(--primary-color); display: flex; align-items: center; justify-content: center;">
                            <i class="bi bi-plus-circle" style="color: var(--primary-color); font-size: 0.8rem;"></i>
                        </div>
                        <small>Klik untuk Lihat/Tempah</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h6><i class="bi bi-bar-chart me-2"></i>Statistik Bulan Ini</h6>
                <div class="row text-center">
                    <div class="col-4">
                        <div class="h5 text-primary mb-0"><?= count($tempahan_bulan) ?></div>
                        <small class="text-muted">Jumlah Tempahan</small>
                    </div>
                    <div class="col-4">
                        <div class="h5 text-success mb-0">
                            <?= count($tempahan_bulan) ?>
                        </div>
                        <small class="text-muted">Diluluskan</small>
                    </div>
                    <div class="col-4">
                        <div class="h5 text-info mb-0">0</div>
                        <small class="text-muted">Menunggu</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Detail Hari -->
<div class="modal fade" id="modalDetailHari" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-calendar-day me-2"></i>Tempahan pada <span id="modalTarikh"></span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="modalContent">
                <!-- Content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                <button type="button" class="btn btn-primary" id="btnBuatTempahan" onclick="buatTempahanTarikhTerpilih()">
                    <i class="bi bi-plus-circle me-2"></i>Buat Tempahan Hari Ini
                </button>
            </div>
        </div>
    </div>
</div>

<?php
$custom_js = "
// Data tempahan untuk JavaScript
const tempahanData = " . json_encode($tempahan_by_date) . ";

// Variable global untuk menyimpan tarikh yang dipilih
let tarikhTerpilih = '';

function lihatTempahanTarikh(tarikh) {
    tarikhTerpilih = tarikh;

    // Format tarikh untuk paparan
    const dateObj = new Date(tarikh);
    const options = {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    };
    const formattedDate = dateObj.toLocaleDateString('ms-MY', options);

    // Set tajuk modal
    document.getElementById('modalTarikh').textContent = formattedDate;

    // Muat tempahan untuk tarikh tersebut
    muatTempahanTarikh(tarikh);

    // Buka modal
    const modal = new bootstrap.Modal(document.getElementById('modalDetailHari'));
    modal.show();
}

function muatTempahanTarikh(tarikh) {
    const modalContent = document.getElementById('modalContent');
    modalContent.innerHTML = '<div class=\"text-center\"><div class=\"spinner-border\" role=\"status\"></div><p class=\"mt-2\">Memuatkan tempahan...</p></div>';

    // Cari tempahan untuk tarikh tersebut dari data yang sudah ada
    const tempahanHari = [];

    // Loop through existing booking data (from PHP)
    if (tempahanData[tarikh]) {
        tempahanData[tarikh].forEach(tempahan => {
            tempahanHari.push(tempahan);
        });
    }

    // Papar tempahan
    paparTempahanHari(tempahanHari, tarikh);
}

function paparTempahanHari(tempahan, tarikh) {
    const modalContent = document.getElementById('modalContent');

    if (tempahan.length === 0) {
        modalContent.innerHTML = '<div class=\"text-center py-4\"><i class=\"bi bi-calendar-x fs-1 text-muted mb-3\"></i><h5 class=\"text-muted\">Tiada Tempahan</h5><p class=\"text-muted\">Tiada tempahan dijumpai untuk tarikh ini.</p><p class=\"text-success\"><i class=\"bi bi-check-circle me-2\"></i>Tarikh ini tersedia untuk tempahan baru!</p></div>';
    } else {
        let html = '<div class=\"row\">';

        tempahan.forEach((t, index) => {
            const statusClass = getStatusClass(t.status);
            const statusText = getStatusText(t.status);

            html += '<div class=\"col-md-6 mb-3\"><div class=\"card\"><div class=\"card-body\">';
            html += '<div class=\"d-flex justify-content-between align-items-start mb-2\">';
            html += '<h6 class=\"card-title mb-0\">' + escapeHtml(t.nama_bilik) + '</h6>';
            html += '<span class=\"badge ' + statusClass + '\">' + statusText + '</span>';
            html += '</div>';
            html += '<p class=\"card-text small mb-2\">';
            html += '<strong>Masa:</strong> ' + formatMasa(t.masa_mula) + ' - ' + formatMasa(t.masa_tamat) + '<br>';
            html += '<strong>Tujuan:</strong> ' + escapeHtml(t.tujuan);
            html += '</p>';
            html += '<small class=\"text-muted\"><i class=\"bi bi-person me-1\"></i>' + escapeHtml(t.nama_pengguna) + '</small>';
            html += '</div></div></div>';
        });

        html += '</div>';

        if (tempahan.length > 0) {
            html += '<div class=\"alert alert-info mt-3\"><i class=\"bi bi-info-circle me-2\"></i><strong>Jumlah tempahan:</strong> ' + tempahan.length + ' tempahan dijumpai untuk tarikh ini. Anda masih boleh membuat tempahan baru jika masa dan bilik yang berbeza.</div>';
        }

        modalContent.innerHTML = html;
    }
}

function buatTempahanTarikhTerpilih() {
    if (!tarikhTerpilih) {
        alert('Sila pilih tarikh dahulu.');
        return;
    }

    // Dapatkan bilik yang sedang dipilih
    const bilikTerpilih = document.getElementById('bilik').value;

    // Set masa mula (8:00 AM) dan tamat (5:00 PM)
    const masaMula = tarikhTerpilih + 'T08:00';
    const masaTamat = tarikhTerpilih + 'T17:00';

    // Buat URL dengan parameter termasuk bilik yang dipilih
    const url = '../BuatTempahan/tempah_bilik.php?' +
                'tarikh_mula=' + encodeURIComponent(masaMula) +
                '&tarikh_tamat=' + encodeURIComponent(masaTamat) +
                '&sesi=3' + // Sesi 3 = Sepanjang Hari
                '&bilik_id=' + encodeURIComponent(bilikTerpilih);

    // Redirect ke halaman tempahan
    window.location.href = url;
}

function formatMasa(timeString) {
    if (timeString && timeString.length >= 5) {
        return timeString.substring(0, 5);
    }
    return timeString;
}

function getStatusClass(status) {
    switch(status.toLowerCase()) {
        case 'menunggu': return 'bg-warning';
        case 'lulus': return 'bg-success';
        case 'diluluskan': return 'bg-success';
        case 'tolak': return 'bg-danger';
        case 'ditolak': return 'bg-danger';
        case 'batal': return 'bg-secondary';
        case 'dibatalkan': return 'bg-secondary';
        case 'selesai': return 'bg-info';
        default: return 'bg-light text-dark';
    }
}

function getStatusText(status) {
    switch(status.toLowerCase()) {
        case 'menunggu': return 'Menunggu';
        case 'lulus': return 'Diluluskan';
        case 'diluluskan': return 'Diluluskan';
        case 'tolak': return 'Ditolak';
        case 'ditolak': return 'Ditolak';
        case 'batal': return 'Dibatalkan';
        case 'dibatalkan': return 'Dibatalkan';
        case 'selesai': return 'Selesai';
        default: return status;
    }
}

function escapeHtml(text) {
    if (!text) return '';
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// Tambah tooltip untuk hari-hari kalendar
document.addEventListener('DOMContentLoaded', function() {
    const calendarDays = document.querySelectorAll('.calendar-day.clickable-day');
    calendarDays.forEach(day => {
        const tarikh = day.getAttribute('data-date');
        const dateObj = new Date(tarikh);
        const options = {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        };
        const formattedDate = dateObj.toLocaleDateString('ms-MY', options);

        day.setAttribute('title', 'Klik untuk lihat tempahan pada ' + formattedDate);
    });
});
";

require_once '../includes/footer_sistem.php';
?>








